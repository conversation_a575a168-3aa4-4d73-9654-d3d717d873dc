"""
Azure DevOps Service - Enhanced version with improved resilience while maintaining original working logic.

This module provides Azure DevOps operations including pull request management, repository operations,
and branch management with consistent logging, error handling, and API resilience.
"""

from blitzy_utils.logger import logger
import base64
import json
import requests
import traceback
from typing import Dict, Any, Optional
from functools import wraps

from src.azure.azure_app_connection import AzureConnection
from src.scm_base.base_classes import BaseAppService
from azure.devops.exceptions import AzureDevOpsServiceError
from src.service.azure_service import fetch_azure_secret
from src.error.errors import AzureBaseError
from blitzy_utils.common import blitzy_exponential_retry


class AzureServiceError(AzureBaseError):
    """Enhanced Azure service exception with operation context."""

    def __init__(
        self,
        message: str,
        operation: str = None,
        status_code: int = None,
        org_id: str = None,
    ):
        self.operation = operation
        self.status_code = status_code
        self.org_id = org_id

        # Build enhanced error message
        context_parts = []
        if operation:
            context_parts.append(f"operation={operation}")
        if status_code:
            context_parts.append(f"status={status_code}")
        if org_id:
            context_parts.append(f"org_id={org_id}")

        context = f" [{', '.join(context_parts)}]" if context_parts else ""
        super().__init__(f"{message}{context}")


def with_operation_logging(operation_name: str):
    """
    Decorator for consistent operation logging and error handling.

    Args:
        operation_name: Name of the operation for logging context
    """

    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Extract parameters for logging context
            org_id = kwargs.get("org_id", "unknown")

            logger.info(f"[AZURE_SERVICE] Starting {operation_name} (org_id: {org_id})")
            logger.debug(f"[AZURE_SERVICE] {operation_name} parameters: {kwargs}")

            try:
                result = func(*args, **kwargs)
                logger.info(f"[AZURE_SERVICE] {operation_name} completed successfully")
                logger.debug(
                    f"[AZURE_SERVICE] {operation_name} result type: {type(result).__name__}"
                )
                return result

            except AzureServiceError as e:
                logger.error(f"[AZURE_SERVICE] {operation_name} failed: {e}")
                raise

            except (AzureBaseError, ValueError) as e:
                error_msg = f"{operation_name} failed: {str(e)}"
                logger.error(f"[AZURE_SERVICE] {error_msg}")
                raise AzureServiceError(
                    error_msg, operation=operation_name, org_id=org_id
                )

            except requests.RequestException as e:
                error_msg = f"Network error in {operation_name}: {str(e)}"
                logger.error(f"[AZURE_SERVICE] {error_msg}")
                raise AzureServiceError(
                    error_msg, operation=operation_name, org_id=org_id
                )

            except Exception as e:
                error_msg = f"Unexpected error in {operation_name}: {str(e)}"
                logger.error(f"[AZURE_SERVICE] {error_msg}")
                logger.debug(
                    f"[AZURE_SERVICE] {operation_name} traceback: {traceback.format_exc()}"
                )
                raise AzureServiceError(
                    error_msg, operation=operation_name, org_id=org_id
                )

        return wrapper

    return decorator


class AzureAppService(BaseAppService):
    """
    Azure DevOps Application Service with enhanced resilience and consistent error handling.

    This service provides a robust interface to Azure DevOps operations including:
    - Pull request management (merge, close)
    - Repository operations (create, get info)
    - Branch operations (get default branch, get head commit)

    Key features:
    - Automatic organization ID to name resolution with multiple fallback methods
    - Consistent logging with operation context
    - Enhanced error handling with detailed error information
    - API resilience with timeout and retry mechanisms
    """

    def __init__(self, access_token=None):
        """
        Initialize Azure DevOps service.

        Args:
            access_token: Optional access token for authentication
        """
        try:
            self.connection = AzureConnection(access_token)
            logger.debug(
                f"[AZURE_SERVICE] AzureConnection initialized: {type(self.connection)}"
            )
        except Exception as e:
            logger.error(f"[AZURE_SERVICE] Failed to initialize AzureConnection: {e}")
            self.connection = None

        self.AZDO_CREATE_REPO_DESCRIPTION = "Repository created automatically"
        self._org_cache = {}  # Cache for org ID to name resolution

        logger.info("[AZURE_SERVICE] Azure DevOps service initialized")

    def _get_auth_headers(self, access_token: str) -> Dict[str, str]:
        """
        Create authentication headers for Azure DevOps API calls.

        Args:
            access_token: Azure DevOps access token

        Returns:
            Dictionary containing authentication headers
        """
        auth_string = f":{access_token}"
        auth_bytes = auth_string.encode("ascii")
        auth_b64 = base64.b64encode(auth_bytes).decode("ascii")

        return {
            "Authorization": f"Basic {auth_b64}",
            "Content-Type": "application/json",
            "Accept": "application/json",
        }

    @blitzy_exponential_retry()
    def _resolve_organization_id(self, org_id: str, access_token: str) -> str:
        """
        Resolve organization ID to organization name using multiple fallback methods.

        This method implements the original working logic with enhanced error handling
        and additional fallback mechanisms for improved resilience.

        Args:
            org_id: Azure DevOps organization ID
            access_token: Azure DevOps access token

        Returns:
            Organization name

        Raises:
            AzureServiceError: If organization cannot be resolved
        """
        logger.debug(f"[AZURE_SERVICE] Resolving organization ID: {org_id}")

        # Check cache first
        if org_id in self._org_cache:
            logger.debug(
                f"[AZURE_SERVICE] Using cached org name: {self._org_cache[org_id]}"
            )
            return self._org_cache[org_id]

        headers = self._get_auth_headers(access_token)
        organization = None

        # Method 1: User profile + organizations (original working method)
        try:
            logger.debug(f"[AZURE_SERVICE] Attempting profile-based org resolution")
            profile_url = "https://app.vssps.visualstudio.com/_apis/profile/profiles/me?api-version=6.0"
            profile_response = requests.get(profile_url, headers=headers, timeout=30)

            if profile_response.status_code == 200:
                profile_data = profile_response.json()
                user_id = profile_data.get("id") or profile_data.get("publicAlias")

                if user_id:
                    orgs_url = f"https://app.vssps.visualstudio.com/_apis/accounts?memberId={user_id}&api-version=6.0"
                    orgs_response = requests.get(orgs_url, headers=headers, timeout=30)

                    if orgs_response.status_code == 200:
                        orgs_data = orgs_response.json()
                        org_count = len(orgs_data.get("value", []))
                        logger.debug(
                            f"[AZURE_SERVICE] Found {org_count} organizations for user"
                        )

                        for org in orgs_data.get("value", []):
                            if org.get("id") == org_id:
                                url = org.get("url", "")
                                if "dev.azure.com/" in url:
                                    organization = url.split("dev.azure.com/")[
                                        -1
                                    ].rstrip("/")
                                elif ".visualstudio.com" in url:
                                    organization = url.split("//")[1].split(".")[0]

                                if organization:
                                    logger.debug(
                                        f"[AZURE_SERVICE] Profile method found org: {organization}"
                                    )
                                    break

        except Exception as e:
            logger.warning(
                f"[AZURE_SERVICE] Profile resolution method failed: {str(e)}"
            )

        # Method 2: Resource areas API (original fallback method)
        if not organization:
            try:
                logger.debug(
                    f"[AZURE_SERVICE] Attempting resource areas org resolution"
                )
                core_area_id = "79134C72-4A58-4B42-976C-04E7115F32BF"
                resource_url = f"https://dev.azure.com/_apis/resourceAreas/{core_area_id}?hostId={org_id}&api-version=5.0-preview.1"
                resource_response = requests.get(
                    resource_url, headers=headers, timeout=30
                )

                if resource_response.status_code == 200:
                    data = resource_response.json()
                    location_url = data.get("locationUrl", "")

                    if location_url and "dev.azure.com/" in location_url:
                        organization = location_url.split("dev.azure.com/")[-1].split(
                            "/"
                        )[0]
                        logger.debug(
                            f"[AZURE_SERVICE] Resource areas method found org: {organization}"
                        )

            except Exception as e:
                logger.warning(
                    f"[AZURE_SERVICE] Resource areas method failed: {str(e)}"
                )

        # Method 3: Known fallbacks (enhanced resilience)
        if not organization:
            logger.debug(f"[AZURE_SERVICE] Attempting known fallback org names")
            potential_names = ["azureblitzy1", "blitzy", "azure-blitzy"]

            for potential_name in potential_names:
                try:
                    test_url = f"https://dev.azure.com/{potential_name}/_apis/connectiondata?api-version=6.0"
                    test_response = requests.get(test_url, headers=headers, timeout=10)

                    if test_response.status_code == 200:
                        data = test_response.json()
                        response_org_id = data.get("instanceId") or data.get(
                            "locationServiceInstanceGuid"
                        )

                        if response_org_id == org_id:
                            organization = potential_name
                            logger.info(
                                f"[AZURE_SERVICE] Fallback method found org: {organization}"
                            )
                            break

                except Exception as e:
                    logger.debug(
                        f"[AZURE_SERVICE] Fallback '{potential_name}' failed: {e}"
                    )

        if organization:
            self._org_cache[org_id] = organization
            logger.info(
                f"[AZURE_SERVICE] Successfully resolved org ID {org_id} to: {organization}"
            )
            return organization

        error_msg = f"Failed to resolve organization ID {org_id} to organization name"
        logger.error(f"[AZURE_SERVICE] {error_msg}")
        raise AzureServiceError(
            error_msg, operation="organization_resolution", org_id=org_id
        )

    def create_branch(
        self, user_id: str, branch_name: str, base_branch: str = "main", **kwargs
    ):
        """
        Create a new branch in a repository.

        Args:
            user_id: ID of user creating the branch
            branch_name: Name of the new branch
            base_branch: Base branch to create from

        Returns:
            Dictionary with operation result

        Raises:
            NotImplementedError: This method is not yet implemented
        """
        raise NotImplementedError("create_branch not yet implemented")

    def get_organization(self, org_name: str, **kwargs):
        """
        Get an organization instance.

        Args:
            org_name: Name of the organization

        Returns:
            Organization instance

        Raises:
            NotImplementedError: This method is not yet implemented
        """
        raise NotImplementedError("get_organization not yet implemented")

    def get_repository(self, repository_id, **kwargs):
        """
        Get a repository from Azure DevOps by ID or name.

        Args:
            repository_id: Repository identifier

        Returns:
            Repository information

        Raises:
            NotImplementedError: This method is not yet implemented
        """
        raise NotImplementedError("get_repository not yet implemented")

    def update_access_token(self, new_access_token: str):
        """
        Update the access token for the connection.

        Args:
            new_access_token: Fresh access token
        """
        logger.info("[AZURE_SERVICE] Updating access token")
        self.connection.update_access_token(new_access_token)

    @blitzy_exponential_retry()
    @with_operation_logging("pull_request_management")
    def manage_pull_request(
        self,
        user_id: str,
        pr_identifier: int,
        action: str,
        merge_method: str = "merge",
        **kwargs,
    ) -> Dict[str, Any]:
        """
        Manage an Azure DevOps pull request.

        Supports merging and closing pull requests with comprehensive error handling
        and automatic organization resolution.

        Args:
            user_id: User performing the action
            pr_identifier: Pull request ID
            action: Action to perform ('merge' or 'close')
            merge_method: Merge method for merge actions ('merge' or 'squash')
            **kwargs: Required kwargs:
                - org_id: Azure DevOps organization ID
                - project_id: Project ID containing the repository
                - repo_id: Repository ID
                - installation_id: Installation ID for authentication

        Returns:
            Dictionary containing operation result with status and details

        Raises:
            AzureServiceError: If operation fails or parameters are invalid
        """
        # Extract and validate required parameters
        org_id = kwargs.get("org_id")
        project_id = kwargs.get("project_id")
        repo_id = kwargs.get("repo_id")
        installation_id = kwargs.get("installation_id")

        missing_params = []
        if not org_id:
            missing_params.append("org_id")
        if not project_id:
            missing_params.append("project_id")
        if not repo_id:
            missing_params.append("repo_id")
        if not installation_id:
            missing_params.append("installation_id")

        if missing_params:
            error_msg = f"Missing required parameters: {', '.join(missing_params)}"
            raise AzureServiceError(error_msg, operation="pull_request_management")

        if action.lower() not in ["merge", "close"]:
            error_msg = f"Unsupported action: {action}. Supported actions: merge, close"
            raise AzureServiceError(error_msg, operation="pull_request_management")

        # Get access token
        secret = fetch_azure_secret(installation_id)
        if not secret or not secret.accessToken:
            raise AzureServiceError(
                "Failed to get access token",
                operation="pull_request_management",
                org_id=org_id,
            )

        access_token = secret.accessToken

        # Resolve organization ID to name
        organization = self._resolve_organization_id(org_id, access_token)

        # Get Azure DevOps connection and git client
        try:
            if not self.connection:
                raise AzureServiceError(
                    "AzureConnection was not properly initialized",
                    operation="pull_request_management",
                    org_id=org_id,
                )

            logger.debug(
                f"[AZURE_SERVICE] Attempting to get client for org: '{organization}'"
            )
            connection = self.connection.get_client(organization, access_token)

            if not connection:
                # Try alternative initialization approach
                logger.warning(
                    "[AZURE_SERVICE] Primary connection failed, trying alternative approach"
                )
                self.connection = AzureConnection(access_token)
                connection = self.connection.get_client(organization, access_token)

                if not connection:
                    raise AzureServiceError(
                        f"Azure connection returned None for organization '{organization}'. "
                        f"This may indicate an authentication or configuration issue.",
                        operation="pull_request_management",
                        org_id=org_id,
                    )

            if not hasattr(connection, "clients"):
                raise AzureServiceError(
                    f"Azure connection missing 'clients' attribute. Connection type: {type(connection)}",
                    operation="pull_request_management",
                    org_id=org_id,
                )

            git_client = connection.clients.get_git_client()
            if not git_client:
                raise AzureServiceError(
                    "Git client returned None from Azure connection",
                    operation="pull_request_management",
                    org_id=org_id,
                )

            logger.debug(
                "[AZURE_SERVICE] Successfully connected to Azure DevOps git client"
            )

        except AzureServiceError:
            raise
        except Exception as e:
            error_msg = f"Failed to connect to Azure DevOps: {str(e)}"
            logger.error(
                f"[AZURE_SERVICE] Connection error details: organization='{organization}', connection_type={type(self.connection)}"
            )
            raise AzureServiceError(
                error_msg, operation="pull_request_management", org_id=org_id
            )

        # Perform the requested action
        try:
            if action.lower() == "merge":
                return self._merge_pull_request(
                    git_client, pr_identifier, project_id, repo_id, merge_method
                )
            elif action.lower() == "close":
                return self._close_pull_request(
                    git_client, pr_identifier, project_id, repo_id
                )

        except Exception as e:
            error_msg = f"Failed to {action} PR {pr_identifier}: {str(e)}"
            raise AzureServiceError(
                error_msg, operation="pull_request_management", org_id=org_id
            )

    @blitzy_exponential_retry()
    def _merge_pull_request(
        self,
        git_client,
        pr_identifier: int,
        project_id: str,
        repo_id: str,
        merge_method: str,
    ) -> Dict[str, Any]:
        """
        Handle pull request merge operation.

        Args:
            git_client: Azure DevOps git client
            pr_identifier: Pull request ID
            project_id: Project ID
            repo_id: Repository ID
            merge_method: Merge method ('merge' or 'squash')

        Returns:
            Dictionary with merge result
        """
        logger.debug(
            f"[AZURE_SERVICE] Getting current PR {pr_identifier} details for merge"
        )
        current_pr = git_client.get_pull_request(
            pull_request_id=pr_identifier, project=project_id, repository_id=repo_id
        )

        # Complete the pull request (merge it)
        merge_result = git_client.update_pull_request(
            git_pull_request_to_update={
                "status": "completed",
                "last_merge_source_commit": current_pr.last_merge_source_commit,
                "completion_options": {
                    "merge_commit_message": f"Merged PR {pr_identifier}",
                    "delete_source_branch": True,
                    "squash_merge": merge_method.lower() == "squash",
                },
            },
            pull_request_id=pr_identifier,
            project=project_id,
            repository_id=repo_id,
        )

        logger.info(f"[AZURE_SERVICE] PR {pr_identifier} merged successfully")
        return {
            "status": "merged",
            "pr_id": pr_identifier,
            "merge_commit": getattr(merge_result, "last_merge_commit", None),
        }

    def _close_pull_request(
        self, git_client, pr_identifier: int, project_id: str, repo_id: str
    ) -> Dict[str, Any]:
        """
        Handle pull request close operation.

        Args:
            git_client: Azure DevOps git client
            pr_identifier: Pull request ID
            project_id: Project ID
            repo_id: Repository ID

        Returns:
            Dictionary with close result
        """
        git_client.update_pull_request(
            git_pull_request_to_update={"status": "abandoned"},
            pull_request_id=pr_identifier,
            project=project_id,
            repository_id=repo_id,
        )

        logger.info(f"[AZURE_SERVICE] PR {pr_identifier} closed successfully")
        return {"status": "closed", "pr_id": pr_identifier}

    @blitzy_exponential_retry()
    @with_operation_logging("get_default_branch")
    def get_default_branch(
        self, repo_id: str, installation_id: str, org_id: str, project: str
    ) -> Dict[str, str]:
        """
        Fetch the default branch name of a specified Azure DevOps repository.

        Args:
            repo_id: Repository ID
            installation_id: Installation ID for authentication
            org_id: Organization ID (will be resolved to organization name)
            project: Project name or ID

        Returns:
            Dictionary containing branch name: {"branch": "branch_name"}

        Raises:
            AzureServiceError: If operation fails
        """
        # Get access token
        secret = fetch_azure_secret(installation_id)
        if not secret or not secret.accessToken:
            raise AzureServiceError(
                "Failed to get access token",
                operation="get_default_branch",
                org_id=org_id,
            )

        access_token = secret.accessToken

        # Resolve organization ID to name
        organization = self._resolve_organization_id(org_id, access_token)

        # Get Azure DevOps connection and git client
        try:
            if not self.connection:
                raise AzureServiceError(
                    "AzureConnection was not properly initialized",
                    operation="get_default_branch",
                    org_id=org_id,
                )

            logger.debug(
                f"[AZURE_SERVICE] Attempting to get client for org: '{organization}'"
            )
            connection = self.connection.get_client(organization, access_token)

            if not connection:
                # Try alternative initialization approach
                logger.warning(
                    "[AZURE_SERVICE] Primary connection failed, trying alternative approach"
                )
                self.connection = AzureConnection(access_token)
                connection = self.connection.get_client(organization, access_token)

                if not connection:
                    raise AzureServiceError(
                        f"Azure connection returned None for organization '{organization}'. "
                        f"This may indicate an authentication or configuration issue.",
                        operation="get_default_branch",
                        org_id=org_id,
                    )

            git_client = connection.clients.get_git_client()
            if not git_client:
                raise AzureServiceError(
                    "Git client returned None from Azure connection",
                    operation="get_default_branch",
                    org_id=org_id,
                )

            logger.debug(
                "[AZURE_SERVICE] Successfully connected to Azure DevOps git client"
            )

        except AzureServiceError:
            raise
        except Exception as e:
            error_msg = f"Failed to connect to Azure DevOps: {str(e)}"
            logger.error(
                f"[AZURE_SERVICE] Connection error details: organization='{organization}', connection_type={type(self.connection)}"
            )
            raise AzureServiceError(
                error_msg, operation="get_default_branch", org_id=org_id
            )

        # Get repository details including default branch
        try:
            logger.debug(f"[AZURE_SERVICE] Getting repository info for repo {repo_id}")
            repo_info = git_client.get_repository(
                repository_id=repo_id, project=project
            )

            default_branch = repo_info.default_branch

            if not default_branch:
                logger.warning(
                    f"[AZURE_SERVICE] No default branch found for repository {repo_id}, defaulting to 'main'"
                )
                return {"branch": "main"}

            # Azure DevOps returns branch names with 'refs/heads/' prefix, so strip it
            if default_branch.startswith("refs/heads/"):
                default_branch = default_branch[len("refs/heads/") :]

            logger.info(
                f"[AZURE_SERVICE] Default branch for repository {repo_id}: {default_branch}"
            )
            return {"branch": default_branch}

        except Exception as e:
            error_msg = f"Failed to get repository info: {str(e)}"
            raise AzureServiceError(
                error_msg, operation="get_default_branch", org_id=org_id
            )

    @blitzy_exponential_retry()
    @with_operation_logging("get_branch_head_commit")
    def get_branch_head_commit(
        self,
        repo_id: str,
        installation_id: str,
        org_id: str,
        project: str,
        branch_name: str,
    ) -> Optional[str]:
        """
        Fetch the head commit of a specific branch from a given Azure DevOps repository.

        Args:
            repo_id: Repository ID
            installation_id: Installation ID for authentication
            org_id: Organization ID (will be resolved to organization name)
            project: Project name or ID
            branch_name: Name of the branch to retrieve the head commit from

        Returns:
            Commit ID of the branch head, or None if not found

        Raises:
            AzureServiceError: If operation fails
        """
        # Get access token
        secret = fetch_azure_secret(installation_id)
        if not secret or not secret.accessToken:
            raise AzureServiceError(
                "Failed to get access token",
                operation="get_branch_head_commit",
                org_id=org_id,
            )

        access_token = secret.accessToken

        # Resolve organization ID to name
        organization = self._resolve_organization_id(org_id, access_token)

        # Get Azure DevOps connection and git client
        try:
            if not self.connection:
                raise AzureServiceError(
                    "AzureConnection was not properly initialized",
                    operation="get_branch_head_commit",
                    org_id=org_id,
                )

            logger.debug(
                f"[AZURE_SERVICE] Attempting to get client for org: '{organization}'"
            )
            logger.debug(
                f"[AZURE_SERVICE] AzureConnection type: {type(self.connection)}"
            )
            logger.debug(
                f"[AZURE_SERVICE] Access token length: {len(access_token) if access_token else 0}"
            )

            connection = self.connection.get_client(organization, access_token)
            logger.debug(
                f"[AZURE_SERVICE] get_client returned: {type(connection)} (None: {connection is None})"
            )

            if not connection:
                # Try alternative initialization approach
                logger.warning(
                    "[AZURE_SERVICE] Primary connection failed, trying alternative approach"
                )
                try:
                    # Re-initialize connection with explicit access token
                    self.connection = AzureConnection(access_token)
                    connection = self.connection.get_client(organization, access_token)
                    logger.debug(
                        f"[AZURE_SERVICE] Alternative connection result: {type(connection)}"
                    )
                except Exception as alt_e:
                    logger.error(
                        f"[AZURE_SERVICE] Alternative connection also failed: {alt_e}"
                    )

                if not connection:
                    raise AzureServiceError(
                        f"Azure connection returned None for organization '{organization}'. "
                        f"This may indicate an authentication or configuration issue.",
                        operation="get_branch_head_commit",
                        org_id=org_id,
                    )

            git_client = connection.clients.get_git_client()
            if not git_client:
                raise AzureServiceError(
                    "Git client returned None from Azure connection",
                    operation="get_branch_head_commit",
                    org_id=org_id,
                )

            logger.debug(
                "[AZURE_SERVICE] Successfully connected to Azure DevOps git client"
            )

        except AzureServiceError:
            raise
        except Exception as e:
            error_msg = f"Failed to connect to Azure DevOps: {str(e)}"
            logger.error(f"[AZURE_SERVICE] Connection error details:")
            logger.error(f"  - Organization: '{organization}'")
            logger.error(f"  - Connection type: {type(self.connection)}")
            logger.error(f"  - Access token present: {bool(access_token)}")
            logger.error(f"  - Error: {str(e)}")
            raise AzureServiceError(
                error_msg, operation="get_branch_head_commit", org_id=org_id
            )

        # Test repository access
        try:
            repo_info = git_client.get_repository(
                repository_id=repo_id, project=project
            )
            logger.debug(
                f"[AZURE_SERVICE] Repository access successful: {repo_info.name}"
            )
        except Exception as e:
            logger.warning(f"[AZURE_SERVICE] Repository access test failed: {str(e)}")

        # Get branches and find the target branch
        try:
            logger.debug(f"[AZURE_SERVICE] Getting branches for repository {repo_id}")
            branches = git_client.get_branches(repository_id=repo_id, project=project)
            logger.debug(f"[AZURE_SERVICE] Found {len(branches)} branches")

            for branch in branches:
                branch_display_name = getattr(branch, "name", "Unknown")

                # Check if this matches our target branch
                if (
                    branch_display_name == branch_name
                    or branch_display_name == f"refs/heads/{branch_name}"
                ):
                    if hasattr(branch, "commit") and branch.commit:
                        commit_id = getattr(branch.commit, "commit_id", None)
                        if commit_id:
                            logger.info(
                                f"[AZURE_SERVICE] Found head commit for branch {branch_name}: {commit_id}"
                            )
                            return commit_id

            logger.warning(
                f"[AZURE_SERVICE] Branch {branch_name} not found among {len(branches)} branches"
            )
            return None

        except Exception as e:
            error_msg = f"Failed to get branches: {str(e)}"
            raise AzureServiceError(
                error_msg, operation="get_branch_head_commit", org_id=org_id
            )

    @blitzy_exponential_retry()
    @with_operation_logging("create_azure_repository")
    def create_azure_repository(self, installation_id: str, repo_name: str, org_id: str, 
                            project: str, private: bool = True) -> Dict[str, Any]:
        """
        Create a new Azure DevOps repository using access token authentication.

        Handles project repositories and automatically initializes the repository
        with a README file if it does not exist.

        Args:
            installation_id: Installation ID to retrieve Azure DevOps access token
            repo_name: Name of the repository to be created
            org_id: Organization ID (will be resolved to organization name)
            project: Project name or ID within the organization
            private: Whether the repository should be private (defaults to True)
                    Note: In Azure DevOps, repository visibility is primarily controlled
                    at the project level. This parameter will set repository permissions
                    to restrict access when private=True.
            

        Returns:
            Dictionary containing created repository information

        Raises:
            AzureServiceError: If repository creation fails
        """
        # Get and validate access token
        secret = fetch_azure_secret(installation_id)
        if not secret or not secret.accessToken:
            raise AzureServiceError(
                "Failed to get access token",
                operation="create_azure_repository",
                org_id=org_id,
            )

        access_token = secret.accessToken

        if not org_id or not project:
            raise AzureServiceError(
                "Missing required parameters: org_id or project",
                operation="create_azure_repository",
                org_id=org_id,
            )

        # Create authentication headers
        headers = self._get_auth_headers(access_token)

        # Resolve organization ID to name
        organization = self._resolve_organization_id(org_id, access_token)

        # Base URL for Azure DevOps REST API
        base_url = f"https://dev.azure.com/{organization}/{project}/_apis"

        # Check if repository already exists
        try:
            repos_url = f"{base_url}/git/repositories?api-version=7.1-preview.1"
            logger.debug(f"[AZURE_SERVICE] Checking if repository '{repo_name}' exists")
            check_response = requests.get(repos_url, headers=headers, timeout=30)

            if check_response.status_code == 401:
                raise AzureServiceError(
                    "Invalid or expired access token",
                    operation="create_azure_repository",
                    status_code=401,
                    org_id=org_id,
                )
            elif check_response.status_code == 403:
                raise AzureServiceError(
                    f"Azure DevOps app lacks necessary permission for {repo_name}",
                    operation="create_azure_repository",
                    status_code=403,
                    org_id=org_id,
                )
            elif check_response.status_code != 200:
                raise AzureServiceError(
                    f"Failed to check existing repositories: {check_response.text}",
                    operation="create_azure_repository",
                    status_code=check_response.status_code,
                    org_id=org_id,
                )

            existing_repos = check_response.json().get("value", [])
            for repo in existing_repos:
                if repo["name"].lower() == repo_name.lower():
                    raise AzureServiceError(
                        f"Repository '{organization}/{project}/{repo_name}' already exists",
                        operation="create_azure_repository",
                        status_code=409,
                        org_id=org_id,
                    )

        except AzureServiceError:
            raise
        except Exception as e:
            error_msg = f"Failed to check existing repositories: {str(e)}"
            raise AzureServiceError(
                error_msg, operation="create_azure_repository", org_id=org_id
            )

        # Create repository
        try:
            repo_data = {"name": repo_name, "project": {"id": project}}

            create_repo_url = f"{base_url}/git/repositories?api-version=7.1-preview.1"
            logger.debug(f"[AZURE_SERVICE] Creating repository '{repo_name}' (private={private})")
            create_response = requests.post(
                create_repo_url, headers=headers, data=json.dumps(repo_data), timeout=30
            )

            if create_response.status_code == 401:
                raise AzureServiceError(
                    "Invalid or expired access token",
                    operation="create_azure_repository",
                    status_code=401,
                    org_id=org_id,
                )
            elif create_response.status_code == 403:
                raise AzureServiceError(
                    f"Azure DevOps app lacks necessary permission for {repo_name}",
                    operation="create_azure_repository",
                    status_code=403,
                    org_id=org_id,
                )
            elif create_response.status_code == 409:
                raise AzureServiceError(
                    f"Repository '{organization}/{project}/{repo_name}' already exists",
                    operation="create_azure_repository",
                    status_code=409,
                    org_id=org_id,
                )
            elif create_response.status_code not in [200, 201]:
                error_data = (
                    create_response.json()
                    if create_response.content
                    else {"message": create_response.text}
                )
                error_msg = f"Azure DevOps error creating repository: {error_data.get('message', str(create_response.text))}"
                raise AzureServiceError(
                    error_msg,
                    operation="create_azure_repository",
                    status_code=create_response.status_code,
                    org_id=org_id,
                )

            repo_info = create_response.json()
            repo_id = repo_info["id"]

        except AzureServiceError:
            raise
        except Exception as e:
            error_msg = f"Failed to create repository: {str(e)}"
            raise AzureServiceError(error_msg, operation="create_azure_repository", org_id=org_id)

        # Set repository permissions based on private parameter
        if private:
            try:
                self._set_repository_permissions_private(
                    organization, project, repo_id, access_token, headers
                )
                logger.debug(f"[AZURE_SERVICE] Set private permissions for repository '{repo_name}'")
            except Exception as e:
                logger.warning(f"[AZURE_SERVICE] Repository created but failed to set private permissions: {str(e)}")

        # Initialize repository with README file
        try:
            self._initialize_repository_with_readme(
                organization, project, repo_id, repo_name, access_token, headers
            )
        except Exception as e:
            logger.warning(f"[AZURE_SERVICE] Repository created but failed to initialize with README: {str(e)}")
        
        logger.info(f"[AZURE_SERVICE] Successfully created repository '{repo_name}' with ID '{repo_id}' (private={private})")
            logger.warning(
                f"[AZURE_SERVICE] Repository created but failed to initialize with README: {str(e)}"
            )

        logger.info(
            f"[AZURE_SERVICE] Successfully created repository '{repo_name}' with ID '{repo_id}'"
        )
        return repo_info

    def _set_repository_permissions_private(self, organization: str, project: str, 
                                        repo_id: str, access_token: str, headers: Dict[str, str]):
        """
        Set repository permissions to make it private by restricting access.
        
        In Azure DevOps, this typically means removing permissions for broader groups
        and ensuring only specific users/groups have access.
        """
        try:
            # Get the security namespace for Git repositories
            security_url = f"https://dev.azure.com/{organization}/_apis/securitynamespaces?api-version=7.1-preview.1"
            security_response = requests.get(security_url, headers=headers, timeout=30)
            
            if security_response.status_code != 200:
                logger.warning(f"[AZURE_SERVICE] Failed to get security namespaces: {security_response.status_code}")
                return
                
            namespaces = security_response.json().get("value", [])
            git_namespace = None
            
            for namespace in namespaces:
                if namespace.get("name") == "Git Repositories":
                    git_namespace = namespace
                    break
            
            if not git_namespace:
                logger.warning("[AZURE_SERVICE] Could not find Git Repositories security namespace")
                return
                
            namespace_id = git_namespace["namespaceId"]
            
            # Set ACL to restrict access - remove permissions for "Everyone" group
            # The token format for repository is: repoV2/{project_id}/{repo_id}
            security_token = f"repoV2/{project}/{repo_id}"
            
            # Get project's default team to remove broad access
            teams_url = f"https://dev.azure.com/{organization}/_apis/projects/{project}/teams?api-version=7.1-preview.3"
            teams_response = requests.get(teams_url, headers=headers, timeout=30)
            
            if teams_response.status_code == 200:
                teams = teams_response.json().get("value", [])
                for team in teams:
                    if team.get("name", "").endswith(" Team"):  # Default project team
                        team_descriptor = team.get("id")
                        if team_descriptor:
                            # Remove read permissions for the default team to make it private
                            acl_url = f"https://dev.azure.com/{organization}/_apis/accesscontrollists/{namespace_id}?api-version=7.1-preview.1"
                            acl_data = {
                                "token": security_token,
                                "merge": True,
                                "accessControlEntries": [
                                    {
                                        "descriptor": f"Microsoft.TeamFoundation.Identity;{team_descriptor}",
                                        "allow": 0,  # No permissions
                                        "deny": 2    # Deny Read (bit 1)
                                    }
                                ]
                            }
                            
                            acl_response = requests.post(
                                acl_url,
                                headers=headers,
                                data=json.dumps(acl_data),
                                timeout=30
                            )
                            
                            if acl_response.status_code not in [200, 201]:
                                logger.warning(f"[AZURE_SERVICE] Failed to set ACL for private repository: {acl_response.status_code}")
                        break
            
        except Exception as e:
            logger.warning(f"[AZURE_SERVICE] Error setting private repository permissions: {str(e)}")
            # Don't raise here as the repository was created successfully

    @blitzy_exponential_retry()
    def _initialize_repository_with_readme(
        self,
        organization: str,
        project: str,
        repo_id: str,
        repo_name: str,
        access_token: str,
        headers: Dict[str, str],
    ) -> None:
        """
        Initialize the repository with a README file by creating an initial commit.

        This replicates the auto_init: True behavior from GitHub.

        Args:
            organization: Azure DevOps organization name
            project: Project name
            repo_id: Repository ID
            repo_name: Repository name
            access_token: Personal Access Token
            headers: HTTP headers for authentication

        Raises:
            Exception: If README initialization fails
        """
        try:
            base_url = f"https://dev.azure.com/{organization}/{project}/_apis"

            # Create README content
            readme_content = f"# {repo_name}\n\n{self.AZDO_CREATE_REPO_DESCRIPTION}\n"
            readme_base64 = base64.b64encode(readme_content.encode("utf-8")).decode(
                "ascii"
            )

            # Create initial commit with README
            push_url = f"{base_url}/git/repositories/{repo_id}/pushes?api-version=7.1-preview.2"

            push_data = {
                "refUpdates": [
                    {
                        "name": "refs/heads/main",
                        "oldObjectId": "0000000000000000000000000000000000000000",
                    }
                ],
                "commits": [
                    {
                        "comment": "Initial commit",
                        "changes": [
                            {
                                "changeType": "add",
                                "item": {"path": "/README.md"},
                                "newContent": {
                                    "content": readme_base64,
                                    "contentType": "base64encoded",
                                },
                            }
                        ],
                    }
                ],
            }

            logger.debug(
                f"[AZURE_SERVICE] Initializing repository '{repo_name}' with README"
            )
            response = requests.post(
                push_url, headers=headers, data=json.dumps(push_data), timeout=30
            )

            if response.status_code not in [200, 201]:
                raise Exception(
                    f"Failed to create initial commit: HTTP {response.status_code}"
                )

            logger.info(
                f"[AZURE_SERVICE] Successfully initialized repository '{repo_name}' with README"
            )

        except Exception as e:
            logger.error(
                f"[AZURE_SERVICE] Failed to initialize repository with README: {str(e)}"
            )
            raise
        
    @blitzy_exponential_retry()
    def get_azure_devops_org_details(self, access_token: str) -> Dict[str, Any]:
        """
        Get organizations for authenticated user with same JSON structure as original method.
        
        Args:
            access_token: OAuth access token for authentication
            
        Returns:
            Dictionary containing organizations and their projects (same format as original)
        """
        
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json",
            "Accept": "application/json"
        }
        
        try:
            # Step 1: Get user profile to extract publicAlias (memberId)
            profile_url = "https://app.vssps.visualstudio.com/_apis/profile/profiles/me?api-version=7.1"
            logger.debug(f"Fetching user profile from: {profile_url}")
            profile_response = requests.get(profile_url, headers=headers, timeout=30)
            profile_response.raise_for_status()
            
            profile_data = profile_response.json()
            logger.debug(f"Profile response data: {profile_data}")
            member_id = profile_data.get("publicAlias") or profile_data.get("id")
            
            if not member_id:
                raise ValueError("Could not extract member ID from user profile")
            
            logger.debug(f"Extracted member ID: {member_id}")
            
            # Step 2: Get organizations using the memberId
            accounts_url = f"https://app.vssps.visualstudio.com/_apis/accounts?memberId={member_id}&api-version=7.1"
            logger.debug(f"Fetching accounts from: {accounts_url}")
            accounts_response = requests.get(accounts_url, headers=headers, timeout=30)
            accounts_response.raise_for_status()
            
            accounts_data = accounts_response.json()
            logger.debug(f"Accounts response data: {accounts_data}")
            user_accounts = accounts_data.get("value", [])
            
            # Step 3: Build response in same format as your original method
            organizations = {}
            
            for account in user_accounts:
                org_name = account.get("accountName")
                org_id = account.get("accountId")
                logger.info(f"Processing organization - Name: {org_name}, ID: {org_id}")
                
                if org_name and org_id:
                    # Get projects for this organization (same logic as original)
                    projects_url = f"https://dev.azure.com/{org_name}/_apis/projects?api-version=7.0"
                    logger.debug(f"Fetching projects for {org_name} from: {projects_url}")
                    
                    try:
                        projects_response = requests.get(projects_url, headers=headers, timeout=30)
                        projects_response.raise_for_status()
                        
                        projects_data = projects_response.json()
                        logger.debug(f"Projects response for {org_name}: {projects_data}")
                        projects = projects_data.get("value", [])
                        
                        logger.info(f"Successfully fetched {len(projects)} projects for organization {org_name}")
                        
                        # Use org_id as key, exact same structure as your original method
                        organizations[org_id] = {
                            "name": org_name,
                            "id": org_id,
                            "type": "Organization",
                            "installationId": org_id,
                            "projects": [
                                {
                                    "name": project.get("name"),
                                    "id": project.get("id"),
                                    "description": project.get("description", ""),
                                    "url": project.get("url", "")
                                }
                                for project in projects
                            ]
                        }
                        
                    except requests.RequestException as e:
                        logger.error(f"Failed to fetch projects for organization {org_name} (ID: {org_id}): {str(e)}")
                        # Re-raise the exception instead of silently failing
                        raise requests.RequestException(f"Failed to fetch projects for organization {org_name}: {str(e)}")
            
            logger.info(f"Successfully processed {len(organizations)} organizations")
            return organizations
            
        except requests.RequestException as e:
            logger.error(f"Failed to fetch Azure DevOps organizations: {str(e)}")
            raise requests.RequestException(f"Failed to fetch Azure DevOps organizations: {str(e)}")
        except (KeyError, ValueError) as e:
            logger.error(f"Invalid response format from Azure DevOps API: {str(e)}")
            raise ValueError(f"Invalid response format from Azure DevOps API: {str(e)}")

