import json
import os
import time
from typing import <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Optional
import base64

import jwt
import requests
from blitzy_utils.logger import logger
from common_models.models import VersionControlSystem

from src.api.models import SecretsOutput
from src.api.routes.secret_manager import get_azure_secret_value
from src.api.utils.secret_manager_utils import create_or_update_azure_secret
from src.error.errors import (AzureAPIError, AzureBaseError,
                              AzureInvalidTokenError, EndpointMisconfigured,
                              ResourceNotFound, TokenExpiredError,
                              TokenRefreshError)
from src.service.github_installation_access_service import \
    get_active_azure_installation_by_user_id

# Module-level constant for retry attempts
MAX_RETRIES = 3


def get_tenant_id_from_token(access_token: str) -> str:
    """
        Extract tenant ID from access token, we use it in azure in a way similar to the installation id from github.
        Tenant id is always the same for all users in an organization, and it never changes even if the organization
        name changes
    """
    try:
        # Decode JWT without verification (we just need the claims)
        decoded_token = jwt.decode(access_token, options={"verify_signature": False})
        if 'tid' not in decoded_token:
            raise AzureInvalidTokenError("No tenant ID found in token")
        tenant_id = decoded_token.get('tid')
        return tenant_id
    except Exception as e:
        raise AzureInvalidTokenError(f"Error decoding token: {e}") from e


def perform_exchange(code: str, redirect_uri: str) -> Tuple[str, str]:
    """
    Exchange authorization code for access and refresh tokens with retry logic and proper logging.

    Args:
        code: Authorization code received from OAuth flow
        redirect_uri: Redirect URI used in the OAuth flow

    Returns:
        Tuple of (access_token, refresh_token) if successful, None otherwise
    """

    # Retrieve configuration from environment variables
    client_id = os.environ.get("AZURE_CLIENT_ID")
    client_secret = os.environ.get("AZURE_CLIENT_SECRET")

    if not client_id:
        raise EndpointMisconfigured("AZURE_CLIENT_ID environment variable not set")
    if not client_secret:
        raise EndpointMisconfigured("AZURE_CLIENT_SECRET environment variable not set")

    data = {
        'client_id': client_id,
        'client_secret': client_secret,
        'code': code,
        'redirect_uri': redirect_uri,
        'grant_type': 'authorization_code',
        'scope': 'https://app.vssps.visualstudio.com/user_impersonation'
    }

    token_url = 'https://login.microsoftonline.com/common/oauth2/v2.0/token'

    logger.info("Starting token exchange process")
    logger.debug(f"Token exchange URL: {token_url}")
    logger.debug(f"Redirect URI: {redirect_uri}")
    logger.debug(f"Grant type: {data['grant_type']}")
    logger.debug(f"Scope: {data['scope']}")

    for attempt in range(1, MAX_RETRIES + 1):
        try:
            logger.info(f"Token exchange attempt {attempt}/{MAX_RETRIES}")
            logger.debug(f"Making POST request to: {token_url}")

            response = requests.post(token_url, data=data, timeout=30)

            logger.debug(f"Response status code: {response.status_code}")
            logger.debug(f"Response headers: {dict(response.headers)}")

            if response.status_code == 200:
                logger.info("Token exchange successful")
                token_data = response.json()
                access_token = token_data.get('access_token')
                refresh_token = token_data.get('refresh_token')

                if access_token and refresh_token:
                    logger.info("Access and refresh tokens retrieved successfully")
                    logger.debug(f"Token type: {token_data.get('token_type', 'N/A')}")
                    logger.debug(f"Expires in: {token_data.get('expires_in', 'N/A')} seconds")
                    return access_token, refresh_token
                elif access_token:
                    logger.debug(f"Token response data: {token_data}")
                    raise AzureAPIError("Only access token found in response, refresh token missing")
                else:
                    logger.debug(f"Token response data: {token_data}")
                    raise AzureAPIError("No tokens found in response")
            else:
                logger.warning(f"Token exchange failed with status {response.status_code}")
                logger.debug(f"Error response: {response.text}")

                if attempt < MAX_RETRIES:
                    wait_time = 2 ** (attempt - 1)  # Exponential backoff: 1s, 2s, 4s
                    logger.info(f"Retrying in {wait_time} seconds...")
                    time.sleep(wait_time)
                else:
                    logger.error(f"Token exchange failed after {MAX_RETRIES} attempts: {response.text}")
                    raise AzureAPIError(f"Token exchange failed. Last status code: {response.status_code}")

        except requests.exceptions.Timeout:
            logger.warning(f"Request timeout on attempt {attempt}/{MAX_RETRIES}")
            if attempt < MAX_RETRIES:
                wait_time = 2 ** (attempt - 1)
                logger.info(f"Retrying in {wait_time} seconds...")
                time.sleep(wait_time)
            else:
                raise AzureAPIError("Token exchange failed due to timeout after all retry attempts")

        except requests.exceptions.ConnectionError as e:
            logger.warning(f"Connection error on attempt {attempt}/{MAX_RETRIES}: {str(e)}")
            if attempt < MAX_RETRIES:
                wait_time = 2 ** (attempt - 1)
                logger.info(f"Retrying in {wait_time} seconds...")
                time.sleep(wait_time)
            else:
                raise AzureAPIError(f"Token exchange failed due to connection error after all retry attempts: {str(e)}")

        except Exception as e:
            logger.error(f"Unexpected error during token exchange: {str(e)}")
            logger.debug(f"Exception details: {type(e).__name__}: {str(e)}")
            raise e
    raise ValueError("Token exchange failed after all retry attempts")


def token_is_expired(access_token: str, buffer_seconds: int = 300) -> bool:
    """
    Returns True if the access token is expired or will expire within buffer_seconds.

    :param access_token: Azure AD access token (JWT format).
    :param buffer_seconds: How many seconds before expiry to consider token expired (default 5 minutes).
    """
    try:
        decoded_token = jwt.decode(access_token, options={"verify_signature": False})
        exp = decoded_token.get("exp")
        if exp is None:
            raise AzureInvalidTokenError("No 'exp' field found in token")
        return time.time() >= (exp - buffer_seconds)
    except Exception as e:
        raise AzureInvalidTokenError(f"Error decoding token for expiration check: {e}") from e


def refresh_azure_access_token(refresh_token: str, redirect_uri: str, used_scope: str) -> json:
    """
    Uses a refresh token to obtain a new Azure AD access token for Azure DevOps API.

    Sends a POST request to the Azure OAuth2 token endpoint with the provided
    refresh token and client credentials to retrieve a fresh access token.

    Concurrency Considerations:
    ---------------------------
    - This function is not inherently thread-safe or process-safe.
    - If called concurrently with the same refresh_token, multiple processes
      may attempt to refresh simultaneously, which can:
        - cause token race conditions,
        - result in overwritten access tokens,
        - or even invalidate the refresh_token depending on backend behavior.
    - It is the responsibility of the caller to:
        - Use a lock/mutex (in-process) or a distributed lock (e.g., Redis, DB row lock)
          if concurrent refreshes might happen across workers.
        - Ensure atomic write/update of the new token in your secret store.

    :param refresh_token: The OAuth2 refresh token to use for obtaining a new access token.
    :param redirect_uri: The redirect URI registered with the Azure AD application.
    :param used_scope: The scope used during token exchange.
    :return: A dictionary containing the token response including 'access_token'.
    :raises TokenExpiredError: If the refresh token has expired and user needs to re-authenticate.
    :raises TokenRefreshError: If token refresh fails for other reasons (invalid credentials, etc.).
    :raises ValueError: If required environment variables are missing.
    """
    client_id = os.environ.get("AZURE_CLIENT_ID")
    client_secret = os.environ.get("AZURE_CLIENT_SECRET")

    if not client_id:
        raise EndpointMisconfigured("Missing required environment variable: AZURE_CLIENT_ID")

    if not client_secret:
        raise EndpointMisconfigured("Missing required environment variable: AZURE_CLIENT_SECRET")

    token_url = "https://login.microsoftonline.com/common/oauth2/v2.0/token"
    payload = {
        'grant_type': 'refresh_token',
        'refresh_token': refresh_token,
        'client_id': client_id,
        'client_secret': client_secret,
        'redirect_uri': redirect_uri,
        'scope': used_scope
    }

    logger.debug(f"Refreshing Azure AD access token with client_id={client_id} and redirect_uri={redirect_uri}")

    try:
        response = requests.post(token_url, data=payload)
        response.raise_for_status()
        token_data = response.json()
        logger.info("Successfully refreshed Azure AD access token.")
        return token_data

    except requests.HTTPError as http_err:
        try:
            error_data = response.json()
            error_code = error_data.get('error', '')
            error_description = error_data.get('error_description', '')
        except (json.JSONDecodeError, AttributeError):
            error_code = ''
            error_description = response.text

        logger.error(f"HTTP error while refreshing token: {http_err} - Error: {error_code} - "
                     f"Description: {error_description}")

        # Check for expired refresh token
        if error_code == 'invalid_grant' and ('expired' in error_description.lower() or
                                              'AADSTS70008' in error_description):
            raise TokenExpiredError("Refresh token has expired. User must re-authenticate.") from http_err

        # Check for other invalid_grant scenarios (malformed token, revoked, etc.)
        elif error_code == 'invalid_grant':
            raise TokenRefreshError(f"Invalid refresh token: {error_description}") from http_err

        # Check for invalid credentials
        elif error_code in ['invalid_client', 'unauthorized_client']:
            raise TokenRefreshError(f"Invalid client credentials: {error_description}") from http_err

        # Other HTTP errors
        else:
            raise TokenRefreshError(f"Token refresh failed: {error_description}") from http_err

    except Exception as err:
        logger.error(f"Unexpected error while refreshing token: {err}")
        raise TokenRefreshError(f"Unexpected error during token refresh: {err}") from err


def get_azure_organizations_by_user(user_info) -> list[dict[str, Any]]:
    """
    Get Azure DevOps organizations for a user.

    This function calls the Azure DevOps REST API to get organizations
    that the user has access to.

    Args:
        user_info: User object containing user information

    Returns:
        List of Azure DevOps organizations

    Raises:
        AzureBaseError: If Azure API call fails
        ResourceNotFound: If no organizations found
    """
    try:
        # Get Azure access token for the user using existing token management
        token_data = fetch_azure_secret_for_user(
            user_info.id, VersionControlSystem.AZURE_DEVOPS
        )
        access_token = token_data.accessToken

        if not access_token:
            raise ResourceNotFound(
                f"No Azure access token found for user {user_info.id}"
            )

        # Call Azure DevOps API to get accounts/organizations
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json",
        }

        # Azure DevOps API endpoint for getting accounts
        url = f"https://app.vssps.visualstudio.com/_apis/accounts?memberId={user_info.id}&api-version=6.0"

        response = requests.get(url, headers=headers)
        response.raise_for_status()

        data = response.json()
        organizations = []

        for account in data.get("value", []):
            organizations.append(
                {
                    "id": account.get("accountId"),
                    "name": account.get("accountName"),
                    "type": "Organization",
                    "url": account.get("accountUri"),
                }
            )

        logger.info(
            f"Found {len(organizations)} Azure organizations for user {user_info.id}"
        )
        return organizations

    except requests.RequestException as e:
        logger.error(
            f"Azure API error getting organizations for user {user_info.id}: {str(e)}"
        )
        raise AzureBaseError(f"Failed to get Azure organizations: {str(e)}")
    except Exception as e:
        logger.error(
            f"Unexpected error getting Azure organizations for user {user_info.id}: {str(e)}"
        )
        raise AzureBaseError(f"Unexpected error: {str(e)}")


def get_azure_repositories_by_user_and_org(
    user_info, org_name: str
) -> list[dict[str, Any]]:
    """
    Get Azure DevOps repositories for a user within a specific organization.

    Args:
        user_info: User object containing user information
        org_name: Azure DevOps organization name

    Returns:
        List of Azure DevOps repositories

    Raises:
        AzureBaseError: If Azure API call fails
        ResourceNotFound: If no repositories found
    """
    try:
        # Get Azure access token for the user using existing token management
        token_data = fetch_azure_secret_for_user(
            user_info.id, VersionControlSystem.AZURE_DEVOPS
        )
        access_token = token_data.accessToken

        if not access_token:
            raise ResourceNotFound(
                f"No Azure access token found for user {user_info.id}"
            )

        headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json",
        }

        # First get all projects in the organization
        projects_url = (
            f"https://dev.azure.com/{org_name}/_apis/projects?api-version=6.0"
        )
        projects_response = requests.get(projects_url, headers=headers)
        projects_response.raise_for_status()

        projects_data = projects_response.json()
        repositories = []

        # For each project, get its repositories
        for project in projects_data.get("value", []):
            project_id = project.get("id")
            project_name = project.get("name")

            repos_url = f"https://dev.azure.com/{org_name}/_apis/git/repositories?api-version=6.0"
            repos_response = requests.get(repos_url, headers=headers)
            repos_response.raise_for_status()

            repos_data = repos_response.json()

            for repo in repos_data.get("value", []):
                # Filter repositories by project
                if repo.get("project", {}).get("id") == project_id:
                    repositories.append(
                        {
                            "id": repo.get("id"),
                            "name": repo.get("name"),
                            "full_name": f"{org_name}/{repo.get('name')}",
                            "project": project_name,
                            "url": repo.get("webUrl"),
                            "private": not repo.get("isPublic", False),
                        }
                    )

        logger.info(
            f"Found {len(repositories)} Azure repositories for user {user_info.id} in org {org_name}"
        )
        return repositories

    except requests.RequestException as e:
        logger.error(
            f"Azure API error getting repositories for user {user_info.id} in org {org_name}: {str(e)}"
        )
        raise AzureBaseError(f"Failed to get Azure repositories: {str(e)}")
    except Exception as e:
        logger.error(
            f"Unexpected error getting Azure repositories for user {user_info.id} in org {org_name}: {str(e)}"
        )
        raise AzureBaseError(f"Unexpected error: {str(e)}")


def get_azure_branches_by_repo(
    user_info, org_name: str, repo_id: str
) -> list[dict[str, Any]]:
    """
    Get Azure DevOps branches for a specific repository.

    Args:
        user_info: User object containing user information
        org_name: Azure DevOps organization name
        repo_id: Azure DevOps repository ID

    Returns:
        List of Azure DevOps branches

    Raises:
        AzureBaseError: If Azure API call fails
        ResourceNotFound: If no branches found
    """
    try:
        # Get Azure access token for the user using existing token management
        token_data = fetch_azure_secret_for_user(
            user_info.id, VersionControlSystem.AZURE_DEVOPS
        )
        access_token = token_data.accessToken

        if not access_token:
            raise ResourceNotFound(
                f"No Azure access token found for user {user_info.id}"
            )

        headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json",
        }

        # Get Git refs (branches) for the repository
        refs_url = f"https://dev.azure.com/{org_name}/_apis/git/repositories/{repo_id}/refs?filter=heads/&api-version=6.0"

        response = requests.get(refs_url, headers=headers)
        response.raise_for_status()

        data = response.json()
        branches = []

        for ref in data.get("value", []):
            ref_name = ref.get("name", "")
            if ref_name.startswith("refs/heads/"):
                branch_name = ref_name.replace("refs/heads/", "")
                branches.append(
                    {
                        "name": branch_name,
                        "ref": ref_name,
                        "sha": ref.get("objectId"),
                        "protected": False,  # Azure DevOps doesn't have a simple protected flag in refs API
                    }
                )

        logger.info(
            f"Found {len(branches)} Azure branches for repo {repo_id} in org {org_name}"
        )
        return branches

    except requests.RequestException as e:
        logger.error(
            f"Azure API error getting branches for repo {repo_id} in org {org_name}: {str(e)}"
        )
        raise AzureBaseError(f"Failed to get Azure branches: {str(e)}")
    except Exception as e:
        logger.error(
            f"Unexpected error getting branches for repo {repo_id} in org {org_name}: {str(e)}"
        )
        raise AzureBaseError(f"Unexpected error: {str(e)}")


def _create_secrets_output(access_token: str, tenant_id: str) -> SecretsOutput:
    """
    Create a SecretsOutput object with the provided access token.

    :param access_token: The access token to include in the output
    :param tenant_id: The tenant ID to use as installation ID
    :return: SecretsOutput object
    """
    return SecretsOutput(
        accessToken=access_token,
        installationID=tenant_id,
        scvType=VersionControlSystem.AZURE_DEVOPS
    )


def _get_valid_token_if_available(tenant_id: str) -> SecretsOutput | None:
    """
    Check if we have a valid (non-expired) access token and return it if available.

    :param tenant_id: The tenant ID to look up the secret for
    :return: SecretsOutput if valid token exists, None otherwise
    """
    try:
        secret = get_azure_secret_value(tenant_id, "latest")
        access_token = secret.get("access_token")

        if access_token and not token_is_expired(access_token):
            logger.info(f"[Azure DevOps] Valid access token found for tenant_id: {tenant_id}")
            return _create_secrets_output(access_token, tenant_id)

    except Exception as e:
        logger.warning(f"[Azure DevOps] Error checking existing token for tenant_id: {tenant_id}: {e}")

    return None


def _refresh_and_store_token(secret: Dict[str, Any], tenant_id: str) -> str:
    """
    Refresh an expired token and store the new credentials.

    :param secret: Dictionary containing refresh_token, redirect_uri, and used_scope
    :param tenant_id: The tenant ID to update the secret for
    :return: The new access token
    :raises TokenExpiredError: If the refresh token is expired
    :raises Exception: If token refresh or storage fails
    """
    refresh_token = secret.get("refresh_token")
    redirect_uri = secret.get("redirect_uri")
    scope = secret.get("used_scope")

    # Refresh the token
    new_secret = refresh_azure_access_token(refresh_token, redirect_uri, scope)
    new_access_token = new_secret["access_token"]
    new_refresh_token = new_secret["refresh_token"]

    # Store the refreshed credentials
    try:
        create_or_update_azure_secret(new_access_token, new_refresh_token, tenant_id, redirect_uri, scope)
        logger.info(f"[Azure DevOps] Refreshed and updated access token for tenant_id: {tenant_id}")
    except Exception as e:
        logger.error(f"[Azure DevOps] Failed to update secret for tenant_id: {tenant_id}: {e}")
        raise

    return new_access_token


def _attempt_token_fetch(tenant_id: str, attempt: int) -> SecretsOutput | None:
    """
    Single attempt to fetch or refresh a DevOps access token.

    :param tenant_id: The tenant ID
    :param attempt: Current attempt number (for logging)
    :return: SecretsOutput if successful, None if should retry
    :raises TokenExpiredError: If refresh token is expired (should not retry)
    :raises Exception: For other fatal errors (should not retry)
    """
    logger.info(f"[Azure DevOps] Attempt {attempt} to fetch access token for tenant_id: {tenant_id}")

    # First, check if we already have a valid token
    valid_token = _get_valid_token_if_available(tenant_id)
    if valid_token:
        return valid_token

    # Token is expired or missing, need to refresh
    logger.info(f"[Azure DevOps] Access token expired or near expiry for tenant_id: {tenant_id}. Refreshing...")

    try:
        secret = get_azure_secret_value(tenant_id, "latest")
        new_access_token = _refresh_and_store_token(secret, tenant_id)
        return _create_secrets_output(new_access_token, tenant_id)

    except TokenExpiredError as e:
        logger.error(f"Refresh Token Expired: {e}")
        raise  # Don't retry on expired refresh token

    except Exception as e:
        logger.warning(f"[Azure DevOps] Failed to refresh token for tenant_id: {tenant_id} on attempt {attempt}: {e}")
        return None  # Indicate should retry


def fetch_azure_secret(tenant_id: str, max_retries=3, retry_delay=1) -> SecretsOutput:
    """
    Fetches and returns the Azure DevOps access token for a given tenant ID.

    This function looks up the stored token associated with the given tenant and attempts to refresh it
    if it's expired or nearing expiration. It retries up to `max_retries` times to handle concurrency or
    transient errors (e.g., token refresh races, network delays).

    :param tenant_id: The Azure AD tenant ID that owns the DevOps connection.
    :param max_retries: Maximum number of retry attempts (default: 3).
    :param retry_delay: Seconds to wait between retries (default: 1 second).
    :return: SecretsOutput object containing a valid access token and SCM type.
    :raises Exception: If a valid token cannot be fetched after the configured retry attempts.
    """
    for attempt in range(1, max_retries + 1):
        try:
            result = _attempt_token_fetch(tenant_id, attempt)
            if result:
                return result

        except (TokenExpiredError, Exception) as e:
            # TokenExpiredError and other fatal errors should not be retried
            if isinstance(e, TokenExpiredError) or attempt == max_retries:
                raise
            logger.error(f"[Azure DevOps] Unexpected error fetching token for tenant_id: {tenant_id} on attempt "
                         f"{attempt}: {e}")

        # Wait before retrying (except on last attempt)
        if attempt < max_retries:
            time.sleep(retry_delay)

    # If we get here, all retries failed
    error_msg = f"Failed to fetch valid Azure DevOps access token for " \
                f"tenant_id: {tenant_id} after {max_retries} retries"
    logger.error(f"[Azure DevOps] {error_msg}")
    raise Exception(error_msg)


def fetch_azure_secret_for_user(user_id: str, scv_type) -> SecretsOutput:
    """
    Fetches Azure DevOps access token for a user by looking up their GitHub installation.

    This function first retrieves the active GitHub installation for the given user,
    then uses the installation_id as the tenant_id to fetch the Azure secret.

    :param user_id: The unique identifier of the user to fetch Azure secret for.
    :return: SecretsOutput object containing a valid access token and SCM type.
    :raises Exception: If GitHub installation is not found for the user.
    :raises TokenExpiredError: If the Azure token is expired and cannot be refreshed.
    :raises AzureBaseError: If there's an error retrieving the access token from Azure DevOps.
    :raises ResourceNotFound: If the access token could not be retrieved from Azure DevOps.
    """

    if scv_type != VersionControlSystem.AZURE_DEVOPS:
        logger.info(f"[Azure DevOps] Incorrect scv_type passed: {scv_type}, \'{VersionControlSystem.AZURE_DEVOPS}\' expected.")
        return
    
    # Get the active installation for this user
    github_installation = get_active_azure_installation_by_user_id(user_id)

    if not github_installation:
        error_msg = f"No active installation found for user_id: {user_id}"
        logger.error(f"[Azure DevOps] {error_msg}")
        raise Exception(error_msg)

    # Use the installation_id as tenant_id to fetch Azure secret
    tenant_id = str(github_installation.installation_id)

    logger.debug(f"[Azure DevOps] Fetching Azure secret for user_id: {user_id} using installation_id: {tenant_id}")

    try:
        token_data = fetch_azure_secret(tenant_id=tenant_id)
    except TokenExpiredError:
        raise
    except Exception as e:
        logger.exception(f"[AccessToken] Error retrieving access token for user_id: {user_id} from Azure DevOps: {e}")
        raise AzureBaseError(f"[AccessToken] Error retrieving access token for user_id: {user_id} from Azure DevOps")

    if not token_data:
        logger.error(f"[AccessToken] Access token for user_id '{user_id}' could not be retrieved from Azure DevOps")
        raise ResourceNotFound(f"Access token for user_id '{user_id}' could not be retrieved from Azure DevOps.")

    return token_data


def fetch_azure_org_id(org_name: str, access_token: str) -> Optional[str]:
    """
    Fetches the Azure DevOps organization ID from the organization name.
    
    :param org_name: The Azure DevOps organization name (e.g., 'azureblitzy1')
    :param access_token: Azure DevOps personal access token or OAuth token
    :return: Organization ID (GUID) if found, None otherwise
    :raises Exception: If there's an error with the API call
    """

    try:
        # Create authentication header
        auth_string = f":{access_token}"
        auth_bytes = auth_string.encode('ascii')
        auth_b64 = base64.b64encode(auth_bytes).decode('ascii')

        headers = {
            "Authorization": f"Basic {auth_b64}",
            "Content-Type": "application/json",
            "Accept": "application/json"
        }

        # Method 1: Use connectiondata API (most reliable)
        logger.info(f"Fetching organization ID for '{org_name}' using connectiondata API")
        connectiondata_url = f"https://dev.azure.com/{org_name}/_apis/connectiondata?api-version=5.0-preview.1"

        response = requests.get(connectiondata_url, headers=headers)

        if response.status_code == 200:
            data = response.json()

            # Try different possible field names for organization ID
            org_id = (data.get('instanceId') or 
                     data.get('locationServiceInstanceGuid') or 
                     data.get('deploymentId'))

            if org_id:
                logger.info(f"Successfully found organization ID: {org_id}")
                return org_id
            else:
                logger.warning("Organization ID not found in connectiondata response")

        elif response.status_code == 401:
            raise Exception("Invalid or expired access token")
        elif response.status_code == 403:
            raise Exception(f"Access denied to organization '{org_name}'. Check permissions.")
        elif response.status_code == 404:
            raise Exception(f"Organization '{org_name}' not found")
        else:
            logger.warning(f"connectiondata API returned status {response.status_code}")

        # Method 2: Try using the Contribution/HierarchyQuery API as fallback
        logger.info("Trying fallback method using HierarchyQuery API")
        hierarchy_url = f"https://dev.azure.com/{org_name}/_apis/Contribution/HierarchyQuery?api-version=5.0-preview.1"

        hierarchy_payload = {
            "contributionIds": ["ms.vss-features.my-organizations-data-provider"],
            "dataProviderContext": {
                "properties": {}
            }
        }

        hierarchy_response = requests.post(
            hierarchy_url, 
            headers=headers, 
            data=json.dumps(hierarchy_payload)
        )

        if hierarchy_response.status_code == 200:
            hierarchy_data = hierarchy_response.json()

            # Navigate through the response structure
            data_providers = hierarchy_data.get('dataProviders', {})
            org_data_provider = data_providers.get('ms.vss-features.my-organizations-data-provider', {})
            organizations = org_data_provider.get('organizations', [])

            # Find the current organization in the list
            for org in organizations:
                if org.get('name') == org_name:
                    org_id = org.get('id')
                    if org_id:
                        logger.info(f"Found organization ID via HierarchyQuery: {org_id}")
                        return org_id

        # Method 3: Try using profile + accounts API (requires additional calls)
        logger.info("Trying fallback method using profile API")

        # First get user profile
        profile_url = "https://app.vssps.visualstudio.com/_apis/profile/profiles/me?api-version=6.0"
        profile_response = requests.get(profile_url, headers=headers)

        if profile_response.status_code == 200:
            profile_data = profile_response.json()
            user_id = profile_data.get('id') or profile_data.get('publicAlias')

            if user_id:
                # Get all organizations for this user
                orgs_url = f"https://app.vssps.visualstudio.com/_apis/accounts?memberId={user_id}&api-version=6.0"
                orgs_response = requests.get(orgs_url, headers=headers)

                if orgs_response.status_code == 200:
                    orgs_data = orgs_response.json()

                    # Find the organization with matching name
                    for org in orgs_data.get('value', []):
                        # Extract org name from URL and compare
                        url = org.get('url', '')
                        url_org_name = None

                        if 'dev.azure.com/' in url:
                            url_org_name = url.split('dev.azure.com/')[-1].rstrip('/')
                        elif '.visualstudio.com' in url:
                            url_org_name = url.split('//')[1].split('.')[0]

                        if url_org_name == org_name:
                            org_id = org.get('id')
                            if org_id:
                                logger.info(f"Found organization ID via profile API: {org_id}")
                                return org_id

        logger.error(f"Could not find organization ID for '{org_name}' using any method")
        return None

    except requests.RequestException as e:
        logger.error(f"Network error while fetching organization ID: {str(e)}")
        raise Exception(f"Network error: {str(e)}")
    except Exception as e:
        logger.error(f"Error fetching organization ID for '{org_name}': {str(e)}")
        raise


def get_azure_projects_by_user_and_org(
    user_info, org_name: str
) -> list[dict[str, Any]]:
    """
    Get Azure DevOps projects for a user within a specific organization.

    Args:
        user_info: User information object containing user ID
        org_name: Azure DevOps organization name

    Returns:
        List of project dictionaries containing project information

    Raises:
        AzureBaseError: If there's an error accessing Azure DevOps API
        ResourceNotFound: If user or organization is not found
    """
    try:
        logger.info(f"Getting Azure projects for user {user_info.id} in org {org_name}")

        # Get Azure access token for the user
        token_data = fetch_azure_secret_for_user(
            user_info.id, VersionControlSystem.AZURE_DEVOPS
        )
        access_token = token_data.accessToken

        # Call Azure DevOps API to get projects
        url = f"https://dev.azure.com/{org_name}/_apis/projects?api-version=6.0"
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json",
        }

        response = requests.get(url, headers=headers, timeout=30)
        response.raise_for_status()

        data = response.json()
        projects = []

        for project in data.get("value", []):
            projects.append(
                {
                    "id": project.get("id"),
                    "name": project.get("name"),
                    "description": project.get("description", ""),
                    "url": project.get("url", ""),
                    "state": project.get("state", ""),
                    "visibility": project.get("visibility", ""),
                }
            )

        logger.info(
            f"Successfully retrieved {len(projects)} projects for org {org_name}"
        )
        return projects

    except requests.exceptions.RequestException as e:
        logger.error(f"Request error getting projects for org {org_name}: {str(e)}")
        raise AzureBaseError(f"Request error: {str(e)}")
    except Exception as e:
        logger.error(f"Unexpected error getting projects for org {org_name}: {str(e)}")
        raise AzureBaseError(f"Unexpected error: {str(e)}")


def get_azure_repositories_by_project_id(
    user_info, org_name: str, project_id: str
) -> list[dict[str, Any]]:
    """
    Get Azure DevOps repositories for a specific project.

    Args:
        user_info: User information object containing user ID
        org_name: Azure DevOps organization name
        project_id: Project ID

    Returns:
        List of repository dictionaries containing repository information

    Raises:
        AzureBaseError: If there's an error accessing Azure DevOps API
        ResourceNotFound: If user, organization, or project is not found
    """
    try:
        logger.info(
            f"Getting Azure repositories for user {user_info.id} in org {org_name}, project {project_id}"
        )

        # Get Azure access token for the user
        token_data = fetch_azure_secret_for_user(
            user_info.id, VersionControlSystem.AZURE_DEVOPS
        )
        access_token = token_data.accessToken

        # Call Azure DevOps API to get repositories for the specific project
        url = f"https://dev.azure.com/{org_name}/{project_id}/_apis/git/repositories?api-version=6.0"
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json",
        }

        response = requests.get(url, headers=headers, timeout=30)
        response.raise_for_status()

        data = response.json()
        repositories = []

        for repo in data.get("value", []):
            repositories.append(
                {
                    "id": repo.get("id"),
                    "name": repo.get("name"),
                    "url": repo.get("webUrl", ""),
                    "clone_url": repo.get("remoteUrl", ""),
                    "default_branch": repo.get("defaultBranch", "").replace(
                        "refs/heads/", ""
                    ),
                    "project": {
                        "id": repo.get("project", {}).get("id"),
                        "name": repo.get("project", {}).get("name"),
                    },
                    "size": repo.get("size", 0),
                    "is_disabled": repo.get("isDisabled", False),
                }
            )

        logger.info(
            f"Successfully retrieved {len(repositories)} repositories for project {project_id} in org {org_name}"
        )
        return repositories

    except requests.exceptions.RequestException as e:
        logger.error(
            f"Request error getting repositories for project {project_id} in org {org_name}: {str(e)}"
        )
        raise AzureBaseError(f"Request error: {str(e)}")
    except Exception as e:
        logger.error(
            f"Unexpected error getting repositories for project {project_id} in org {org_name}: {str(e)}"
        )
        raise AzureBaseError(f"Unexpected error: {str(e)}")


def get_azure_branches_by_project_and_repo_id(
    user_info, org_name: str, project_id: str, repo_id: str
) -> list[dict[str, Any]]:
    """
    Get Azure DevOps branches for a specific repository within a project.

    Args:
        user_info: User information object containing user ID
        org_name: Azure DevOps organization name
        project_id: Project ID
        repo_id: Repository ID

    Returns:
        List of branch dictionaries containing branch information

    Raises:
        AzureBaseError: If there's an error accessing Azure DevOps API
        ResourceNotFound: If user, organization, project, or repository is not found
    """
    try:
        logger.info(
            f"Getting Azure branches for user {user_info.id} in org {org_name}, project {project_id}, repo {repo_id}"
        )

        # Get Azure access token for the user
        token_data = fetch_azure_secret_for_user(
            user_info.id, VersionControlSystem.AZURE_DEVOPS
        )
        access_token = token_data.accessToken

        # Call Azure DevOps API to get branches for the specific repository
        url = f"https://dev.azure.com/{org_name}/{project_id}/_apis/git/repositories/{repo_id}/refs?filter=heads/&api-version=6.0"
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json",
        }

        response = requests.get(url, headers=headers, timeout=30)
        response.raise_for_status()

        data = response.json()
        branches = []

        for ref in data.get("value", []):
            if ref.get("name", "").startswith("refs/heads/"):
                branch_name = ref["name"].replace("refs/heads/", "")
                branches.append(
                    {
                        "name": branch_name,
                        "ref": ref.get("name"),
                        "sha": ref.get("objectId"),
                        "protected": False,  # Azure DevOps doesn't have a simple protected flag in refs API
                    }
                )

        logger.info(
            f"Successfully retrieved {len(branches)} branches for repo {repo_id} in project {project_id}, org {org_name}"
        )
        return branches

    except requests.exceptions.RequestException as e:
        logger.error(
            f"Request error getting branches for repo {repo_id} in project {project_id}, org {org_name}: {str(e)}"
        )
        raise AzureBaseError(f"Request error: {str(e)}")
    except Exception as e:
        logger.error(
            f"Unexpected error getting branches for repo {repo_id} in project {project_id}, org {org_name}: {str(e)}"
        )
        raise AzureBaseError(f"Unexpected error: {str(e)}")
