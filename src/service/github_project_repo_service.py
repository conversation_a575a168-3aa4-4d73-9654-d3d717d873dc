from typing import Any, Dict, List, Optional

from common_models.db_client import get_db_session
from common_models.models import (GitHubBranchPattern, GitHubProjectRepo,
                                  UsageType)
from sqlalchemy.orm import Session


def get_github_project_repo_by_org_repo_branch_company_name(org: str, repo: str, branch_name: str, company_id: str,
                                                            session: Optional[Session] = None):
    """
    Fetches a GitHub project repository by organization name, repository name,
    and branch name. This function queries the database for the corresponding
    GitHub branch pattern using the provided parameters and returns the result.

    :param org: The organization name for the GitHub repository.
    :param repo: The repository name under the specified organization.
    :param branch_name: The specific branch name within the repository.
    :param company_id: ID of the company.
    :param session: An optional SQLAlchemy session. If provided, it will
                    reuse the session. Otherwise, a new session will
                    be managed internally.
    :return: The first matching GitHubBranchPattern object, or None if no
             match is found.
    """
    with get_db_session(session) as session:
        github_branch_pattern = (session.query(GitHubBranchPattern).filter(
            GitHubBranchPattern.org_name == org,
            GitHubBranchPattern.repo_name == repo,
            GitHubBranchPattern.branch_name == branch_name,
            GitHubBranchPattern.company_id == company_id
        ).first())

        if github_branch_pattern and not session:
            session.expunge(github_branch_pattern)
        return github_branch_pattern


def get_github_project_repo_by_repo_and_org(
    repo_id: str, org_id: str, session: Optional[Session] = None
) -> GitHubProjectRepo:
    """
    Fetches a GitHub project repository by repository ID and organization ID.

    For Azure DevOps repositories, this function looks up using azure_org_id field.
    For GitHub repositories, this function looks up using org_id field.

    :param repo_id: The identifier of the repository.
    :type repo_id: str
    :param org_id: The organization ID (for Azure DevOps, this is the Azure organization ID).
    :type org_id: str
    :param session: An optional database session.
    :type session: Optional[Session]
    :return: The matching GitHubProjectRepo instance or None if not found.
    :rtype: Optional[GitHubProjectRepo]
    """
    with get_db_session(session) as session:
        # First try to find by azure_org_id (for Azure DevOps repositories)
        project_repo = (
            session.query(GitHubProjectRepo)
            .filter_by(repo_id=repo_id, azure_org_id=org_id)
            .first()
        )

        # If not found, try to find by org_id (for GitHub repositories)
        if not project_repo:
            project_repo = (
                session.query(GitHubProjectRepo)
                .filter_by(repo_id=repo_id, org_id=org_id)
                .first()
            )

        if project_repo and not session:
            session.expunge(project_repo)
        return project_repo


def get_github_project_repo_by_project_id(project_id: str,
                                          session: Optional[Session] = None) -> Optional[List[GitHubProjectRepo]]:
    """
    Retrieve GitHub project repository records associated with a specified project ID.

    This function queries the database to find all GitHub project repository records
    that match the provided project ID. Optionally, a database session can be supplied;
    if none is provided, a new session will be created and managed internally. If repositories
    are located and no external session is provided, all retrieved objects will be detached
    from the session to ensure they can be used independently.

    :param project_id: The unique identifier of the project for which GitHub
        repository records are being retrieved.
    :param session: An optional SQLAlchemy database session object. If provided,
        this session will be used; otherwise, a new session will be created internally.
    :return: A list of GitHubProjectRepo objects if any are found, otherwise None.
    """
    with get_db_session(session) as session:
        github_project_repos = (session.query(GitHubProjectRepo).filter(
            GitHubProjectRepo.project_id == project_id).all())

        if github_project_repos and not session:
            session.expunge_all(github_project_repos)
        return github_project_repos


# This only retrieves single source github repo.
def get_source_github_project_repo_by_id(project_id: str,
                                         session: Optional[Session] = None) -> Optional[GitHubProjectRepo]:
    """
    Get source github repo by project id.
    :param project_id: Project ID.
    :param session: Client session if any.
    :return: Github project repo object.
    """
    with get_db_session(session) as session:
        github_project_repos = (session.query(GitHubProjectRepo).filter(
            GitHubProjectRepo.project_id == project_id,
            GitHubProjectRepo.usage_type == UsageType.SOURCE
        ).first())

        if github_project_repos and not session:
            session.expunge(github_project_repos)
        return github_project_repos


def get_source_github_project_repo_by_repo_id(
        repo_id: str,
        session: Optional[Session] = None
) -> Optional[GitHubProjectRepo]:
    """
    Get source ONLY github project repo by repo ID.
    :param repo_id: Repository ID.
    :param session: Client session if any.
    :return: Github project repo object.
    """
    with get_db_session(session) as session:
        github_project_repos = (session.query(GitHubProjectRepo).filter(
            GitHubProjectRepo.repo_id == repo_id,
            GitHubProjectRepo.usage_type == UsageType.SOURCE
        ).first())

        if github_project_repos and not session:
            session.expunge(github_project_repos)
        return github_project_repos


def get_github_branch_pattern_by_repository_id_and_branch_name(repo_id: str, branch_name: str) \
        -> Optional[GitHubBranchPattern]:
    """
    Gets github branch pattern by repository id and branch name.
    :param repo_id: Repository ID.
    :param branch_name: Branch name.
    :param session: Client session if any.
    :return: Github branch pattern object.
    """
    with get_db_session() as session:
        github_branch_pattern = (session.query(GitHubBranchPattern).filter(
            GitHubBranchPattern.repo_id == repo_id,
            GitHubBranchPattern.branch_name == branch_name
        ).first())
        return github_branch_pattern


# This only retrieves single target github repo.
def get_target_github_project_repo_by_id(project_id: str,
                                         session: Optional[Session] = None) -> Optional[GitHubProjectRepo]:
    """
    Get target github repo by project id.
    :param project_id: Project ID.
    :param session: Client session if any.
    :return: Github project repo object.
    """
    with get_db_session(session) as session:
        github_project_repos = (session.query(GitHubProjectRepo).filter(
            GitHubProjectRepo.project_id == project_id,
            GitHubProjectRepo.usage_type == UsageType.TARGET
        ).first())

        if github_project_repos and not session:
            session.expunge(github_project_repos)
        return github_project_repos


def get_github_project_repo_by_project_id_and_by_github_info(project_id: str, org_name: str, repo_id: str,
                                                             branch_name: str, usage_type: UsageType,
                                                             session: Optional[Session] = None) -> GitHubProjectRepo:
    """
    Fetches a GitHub project repository record based on the provided project ID, organization
    name, repository ID, branch name, and usage type.

    :param project_id: Unique identifier for the project.
    :type project_id: str
    :param org_name: Name of the organization owning the repository.
    :type org_name: str
    :param repo_id: Unique identifier for the repository.
    :type repo_id: str
    :param branch_name: Branch name of the repository.
    :type branch_name: str
    :param usage_type: Usage type of the GitHub repository.
    :type usage_type: UsageType
    :param session: Optional database session.
    :type session: Optional[Session]
    :return: The matching GitHubProjectRepo instance or None if not found.
    :rtype: GitHubProjectRepo
    """
    with get_db_session(session) as session:
        github_project_repo = (session.query(GitHubProjectRepo).filter(
            GitHubProjectRepo.project_id == project_id,
            GitHubProjectRepo.org_name == org_name,
            GitHubProjectRepo.repo_id == repo_id,
            GitHubProjectRepo.branch_name == branch_name,
            GitHubProjectRepo.usage_type == usage_type
        ).first())

        if github_project_repo and not session:
            session.expunge(github_project_repo)
        return github_project_repo


def update_github_project_repo_by_id(project_repo_id: str, update_payload: Dict[Any, Any],
                                     session: Optional[Session] = None):
    """
    Updates a GitHub project repository entry by its ID in the database.

    :param project_repo_id: The unique identifier of the GitHub project repository.
    :type project_repo_id: str
    :param update_payload: Data to update the repository with.
    :type update_payload: Dict[Any, Any]
    :param session: Optional database session for the operation.
    :type session: Optional[Session]
    :return: Whether the update operation succeeded or not.
    :rtype: None
    :raises Exception: If the update operation fails.
    """

    with get_db_session(session) as session:
        github_project_repo_updated = session.query(GitHubProjectRepo).filter(
            GitHubProjectRepo.id == project_repo_id,
        ).update(update_payload)

        if not github_project_repo_updated:
            raise Exception(f"Failed to update github project repo table for project {project_repo_id}")


def update_github_project_by_project_id(project_id: str, update_payload: Dict[Any, Any],
                                        session: Optional[Session] = None) -> Any:
    """
    Updates a GitHub project repository identified by project_id using the provided payload.

    :param project_id: The branch name of the repository to be updated.
    :type project_id: Str
    :param update_payload: Key-value pairs containing updates to be applied to the repository.
    :type update_payload: Dict[Any, Any]
    :param session: Optional database session for the operation.
    :type session: Optional[Session]
    :return: The result of the update operation.
    """
    with get_db_session(session) as session:
        github_project_updated = session.query(GitHubProjectRepo).filter(
            GitHubProjectRepo.project_id == project_id,
        ).update(update_payload)

        if not github_project_updated:
            raise Exception(
                f"Failed to update github project repo table for project {project_id}")

        return github_project_updated
