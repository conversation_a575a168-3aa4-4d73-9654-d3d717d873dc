from typing import Any, Dict, List

from blitzy_utils.logger import logger
from flask import Blueprint, jsonify
from common_models.models import User, VersionControlSystem
from flask_utils.decorators import flask_pydantic_response, validate_request
from flask_utils.models_config.model_utils import map_to_model
from types import MappingProxyType
from src.github.github_app_connection import GithubAppConnection
from src.azure.azure_app_connection import AzureConnection

from src.api.models import (
    Branch,
    BranchList,
    DefaultBranchOutput,
    GetGithubInstallationOutput,
    GetGithubInstallationOutputArray,
    GithubOrgBasic,
    GithubOrgBasicList,
    PRActionInput,
    Repository,
    RepositoryList,
    AzureOrgBasic,
    AzureOrgBasicList,
    AzureProject,
    AzureProjectList,
)
from src.api.utils.github_utils import (get_branches_by_repo_id,
                                        get_orgs_by_installations,
                                        get_repo_by_id,
                                        get_repos_by_installation)
from src.error.errors import ResourceNotFound
from src.github.github_app_service import GithubAppService


from src.service.github_installation_access_service import (
    get_active_github_installation_by_repo_id,
    get_github_installation_by_user_and_target_name,
    get_github_installations_by_user,)
from src.service.github_project_repo_service import \
    get_github_project_repo_by_repo_and_org
from src.service.user_service import get_user_by_id

users_bp = Blueprint("users", __name__, url_prefix="/users")


@users_bp.route("/<user_id>/installations", methods=["GET"])
@flask_pydantic_response
def get_installation_by_user_id(user_id: str):
    """Get the github installation by user id."""
    user_info = get_user_by_id(user_id)
    if not user_info:
        raise ResourceNotFound(f"User with id {user_id} not found")
    installation_list = get_github_installations_by_user(user_id)
    result = map_installation_list_to_model(installation_list)
    return result, 200


@users_bp.route("/<user_id>/organizations", methods=["GET"])
@flask_pydantic_response
def get_organizations_by_user_id(user_id: str):
    """Get organizations by user id - supports both GitHub and Azure DevOps based on user's installation type."""
    user_info = get_user_by_id(user_id)
    if not user_info:
        raise ResourceNotFound(f"User with id {user_id} not found")

    # Get user's installations to determine service type
    installation_list = get_github_installations_by_user(user_id)
    if not installation_list:
        raise ResourceNotFound(f"No installations found for user {user_id}")

    # Check if user has Azure DevOps installations
    azure_installations = [
        inst
        for inst in installation_list
        if inst.svc_type == VersionControlSystem.AZURE_DEVOPS
    ]

    if azure_installations:
        # Use Azure DevOps service to get organizations
        from src.service.azure_service import get_azure_organizations_by_user

        org_list = get_azure_organizations_by_user(user_info)
        response = map_azure_org_list_to_pydantic(org_list)
    else:
        # Use GitHub service to get organizations
        org_list = get_organization_by_user(user_info)
        response = map_org_list_to_pydantic(org_list)

    return response, 200


@users_bp.route("/<user_id>/organizations/<org_name>/repositories", methods=["GET"])
@flask_pydantic_response
def get_repositories_by_user_id(user_id: str, org_name: str):
    """Get repositories by user id and organization - supports both GitHub and Azure DevOps."""
    user_info = get_user_by_id(user_id)
    if not user_info:
        raise ResourceNotFound(f"User with id {user_id} not found")

    # Get installation to determine service type
    installation = get_github_installation_by_user_and_target_name(user_id, org_name)
    if not installation:
        raise ResourceNotFound(
            f"Installation for user {user_id} and org {org_name} not found."
        )

    if installation.svc_type == VersionControlSystem.AZURE_DEVOPS:
        # For Azure DevOps, get repositories from all projects in the organization
        # Note: org_name parameter is treated as org_id for Azure DevOps
        from src.service.azure_service import get_azure_repositories_by_user_and_org

        repo_list = get_azure_repositories_by_user_and_org(user_info, org_name)
        response = map_azure_repo_list_to_pydantic(repo_list)
    else:
        # Use GitHub service to get repositories
        repo_list = get_repos_by_user(user_info, org_name)
        response = map_repo_list_to_pydantic(repo_list)

    return response, 200


@users_bp.route("/<user_id>/organizations/<org_name>/projects", methods=["GET"])
@flask_pydantic_response
def get_projects_by_user_id(user_id: str, org_name: str):
    """Get projects by user id and organization - supports Azure DevOps only."""
    user_info = get_user_by_id(user_id)
    if not user_info:
        raise ResourceNotFound(f"User with id {user_id} not found")

    installation = get_github_installation_by_user_and_target_name(user_id, org_name)
    if not installation:
        raise ResourceNotFound(
            f"Installation for user {user_id} and org {org_name} not found."
        )

    if installation.svc_type == VersionControlSystem.AZURE_DEVOPS:
        # Note: org_name parameter is treated as org_id for Azure DevOps
        from src.service.azure_service import get_azure_projects_by_user_and_org

        project_list = get_azure_projects_by_user_and_org(user_info, org_name)
        response = map_azure_project_list_to_pydantic(project_list)
    else:
        # GitHub doesn't have projects in the same way as Azure DevOps
        response = {"results": []}

    return response, 200


@users_bp.route(
    "/<user_id>/organizations/<org_name>/projects/<project_id>/repositories",
    methods=["GET"],
)
@flask_pydantic_response
def get_repositories_by_project_id(user_id: str, org_name: str, project_id: str):
    """Get repositories by user id, organization, and project ID - supports Azure DevOps only."""
    user_info = get_user_by_id(user_id)
    if not user_info:
        raise ResourceNotFound(f"User with id {user_id} not found")

    installation = get_github_installation_by_user_and_target_name(user_id, org_name)
    if not installation:
        raise ResourceNotFound(
            f"Installation for user {user_id} and org {org_name} not found."
        )

    if installation.svc_type == VersionControlSystem.AZURE_DEVOPS:
        # Note: org_name parameter is treated as org_id for Azure DevOps
        from src.service.azure_service import get_azure_repositories_by_project_id

        repo_list = get_azure_repositories_by_project_id(
            user_info, org_name, project_id
        )
        response = map_azure_repo_list_to_pydantic(repo_list)
    else:
        # GitHub doesn't have projects in the same way as Azure DevOps
        raise ResourceNotFound(
            "Project-based repositories are only supported for Azure DevOps"
        )

    return response, 200


@users_bp.route("/<user_id>/organizations/<org_name>/repositories/<repo_id>/branches", methods=["GET"])
@flask_pydantic_response
def get_branches_by_repo_id_and_installation_id(user_id: str, org_name: str, repo_id: str):
    """Get branches by repo id - supports both GitHub and Azure DevOps."""
    user_info = get_user_by_id(user_id)
    if not user_info:
        raise ResourceNotFound(f"User with id {user_id} not found")

    installation = get_github_installation_by_user_and_target_name(user_id, org_name)
    if not installation:
        raise ResourceNotFound(f"Installation for user {user_id} and org {org_name} not found.")

    if installation.svc_type == VersionControlSystem.AZURE_DEVOPS:
        # For Azure DevOps, get branches directly using repository ID
        # Note: org_name parameter is treated as org_id for Azure DevOps
        from src.service.azure_service import get_azure_branches_by_repo

        branch_list = get_azure_branches_by_repo(user_info, org_name, repo_id)
        if not branch_list:
            raise ResourceNotFound(f"Branches not found for Azure repo {repo_id}")
        output = map_azure_branches_to_pydantic(branch_list)
    else:
        # Use GitHub service to get branches
        branch_list = get_branches_by_repo_id(installation.installation_id, repo_id)
        if not branch_list:
            raise ResourceNotFound(f"Branches not found for repo {repo_id}")
        output = map_branches_to_pydantic(branch_list)

    return output, 200


@users_bp.route("/<user_id>/organizations/<org_name>/repositories/<repo_id>", methods=["GET"])
@flask_pydantic_response
def get_repo_by_repo_id(user_id: str, org_name: str, repo_id: str):
    user_info = get_user_by_id(user_id)
    if not user_info:
        raise ResourceNotFound(f"User with id {user_id} not found")

    installation = get_github_installation_by_user_and_target_name(user_id, org_name)
    if not installation:
        raise ResourceNotFound(f"Installation for user {user_id} and org {org_name} not found.")

    repo_response = get_repo_by_id(installation.installation_id, repo_id)
    if not repo_response:
        raise ResourceNotFound(f"Repository with id {repo_id} not found")

    response = Repository(**repo_response)
    return response, 200


@users_bp.route("/<user_id>/organizations/<org_name>/repositories/<repo_id>/default/branch", methods=["GET"])
@flask_pydantic_response
def get_default_branches_by_repo_id(user_id: str, org_name: str, repo_id: str):
    user_info = get_user_by_id(user_id)
    if not user_info:
        raise ResourceNotFound(f"User with id {user_id} not found")

    installation = get_github_installation_by_user_and_target_name(user_id, org_name)
    if not installation:
        raise ResourceNotFound(f"Installation for user {user_id} and org {org_name} not found.")

    if installation.svc_type == VersionControlSystem.AZURE_DEVOPS:
        github_project_info = get_github_project_repo_by_repo_and_org(repo_id, org_name)
        if not github_project_info:
            raise ResourceNotFound(f"Installation for user {user_id} and org {org_name} not found.")

        from src.azure.azure_app_service import AzureAppService

        azure_service = AzureAppService()
        branch_output = azure_service.get_default_branch(repo_id, installation.installation_id, github_project_info.azure_org_id, github_project_info.azure_project_id)
        response = DefaultBranchOutput(**branch_output)

    elif installation.svc_type == VersionControlSystem.GITHUB:
        github_service = GithubAppService()
        branch_output = github_service.get_default_branch(repo_id, installation.installation_id)
        response = DefaultBranchOutput(**branch_output)
    else:
        raise ResourceNotFound(f"Active SVC type for installation {installation.installation_id} not found.")

    return response, 200


# TODO: consider rethinking api, branch name still can cause issues if has postfil /head
@users_bp.route("/<user_id>/organizations/<org_name>/repositories/<repo_id>/branch/<path:branch_name>/head/commit",
                methods=["GET"])
@flask_pydantic_response
def get_branch_head_commit(user_id: str, org_name: str, repo_id: str, branch_name: str):
    user_info = get_user_by_id(user_id)
    if not user_info:
        raise ResourceNotFound(f"User with id {user_id} not found")

    installation = get_github_installation_by_user_and_target_name(user_id, org_name)
    if not installation:
        raise ResourceNotFound(f"Installation for user {user_id} and org {org_name} not found.")

    if installation.svc_type == VersionControlSystem.AZURE_DEVOPS:
        from src.azure.azure_app_service import AzureAppService

        azure_service = AzureAppService()

        project_repo_info = get_github_project_repo_by_repo_and_org(repo_id, org_name)
        if not project_repo_info:
            raise ResourceNotFound(f"Project Repo Info for repo {repo_id} and org {org_name} not found.")

        branch_commit_output = azure_service.get_branch_head_commit(
            repo_id,
            installation.installation_id,
            project_repo_info.azure_org_id,
            project_repo_info.azure_project_id,
            branch_name,
        )
        if not branch_commit_output:
            raise ResourceNotFound(f"Commit information not found for {branch_name}")

    elif installation.svc_type == VersionControlSystem.GITHUB:
        github_service = GithubAppService()
        branch_commit_output = github_service.get_branch_head_commit(repo_id, installation.installation_id, branch_name)

        if not branch_commit_output:
            raise ResourceNotFound(f"Commit information not found for {branch_name}")
    else:
        raise ResourceNotFound(f"Active SVC type for installation {installation.installation_id} not found.")

    return branch_commit_output, 200


@users_bp.route("/<user_id>/organizations/<org_name>/repositories/<repo_id>/pr/<pr_number>/action",
                methods=["POST"])
@validate_request(PRActionInput)
@flask_pydantic_response
def post_pr_action_by_repo_id(user_id: str, org_name: str, repo_id: str, pr_number: str, payload: PRActionInput):
    user_info = get_user_by_id(user_id)
    if not user_info:
        raise ResourceNotFound(f"User with id {user_id} not found")

    installation = get_github_installation_by_user_and_target_name(user_id, org_name)
    if not installation:
        raise ResourceNotFound(f"Installation for user {user_id} and org {org_name} not found.")

    scm_service = MappingProxyType(
        {
            VersionControlSystem.GITHUB: GithubAppConnection,
            VersionControlSystem.AZURE_DEVOPS: AzureConnection,
        }
    )[installation.svc_type]

    if not scm_service:
        raise ResourceNotFound(f"Service Factory for scv type: {installation.svc_type} not found.")

    if installation.svc_type == VersionControlSystem.GITHUB:
        pr_action_status = scm_service.manage_pull_request(user_info.id, int(pr_number), payload.action.value, "merge",
                                                          installation_id=installation.installation_id, repo_id=repo_id)
    elif installation.svc_type == VersionControlSystem.AZURE_DEVOPS:

        project_repo_info = get_github_project_repo_by_repo_and_org(repo_id, org_name)
        if not project_repo_info:
            raise ResourceNotFound(f"Project Repo Info for repo {repo_id} and org {org_name} not found.")

        pr_action_status = scm_service.manage_pull_request("", int(pr_number), payload.action.value, "merge",
                                                          organization=project_repo_info.azure_org_id, project_id=project_repo_info.azure_project_id, repo_id=repo_id, installation_id=installation.installation_id)
    else:
        raise ResourceNotFound(f"Active SVC type for installation {installation.installation_id} not found.")

    return pr_action_status, 200


@users_bp.route("/<user_id>/organizations/<org_name>/repositories/<repo_id>/pr/<pr_number>/status", methods=["GET"])
def get_pr_status_by_repo_id(user_id: str, org_name: str, repo_id: str, pr_number: str):
    """Get PR status by repository ID and pull request number."""
    user_info = get_user_by_id(user_id)
    if not user_info:
        raise ResourceNotFound(f"User with id {user_id} not found")

    installation = get_github_installation_by_user_and_target_name(user_id, org_name)
    if not installation:
        raise ResourceNotFound(f"Installation for user {user_id} and org {org_name} not found.")

    try:
        pr_number_int = int(pr_number)
    except ValueError:
        raise ResourceNotFound(f"Invalid PR number: {pr_number}")
    
    # TODO: add azure devops support also for PR status
    if installation.svc_type == VersionControlSystem.GITHUB:
        github_service = GithubAppService()
        # Get the GitHub installation for this repository
        github_repo_installation = get_active_github_installation_by_repo_id(repo_id)
        if not github_repo_installation:
            raise ResourceNotFound(f"No active GitHub installation found for repo_id: {repo_id}")
        
        pr_status = github_service.get_pr_status_by_repo_id_and_pr_number(repo_id, pr_number_int, github_repo_installation.installation_id)

    if not pr_status:
        raise ResourceNotFound(f"PR with number {pr_number} not found for repository {repo_id}")

    return jsonify({"status": pr_status}), 200


def map_installation_list_to_model(installation_list) -> GetGithubInstallationOutputArray:
    mapped_list = [map_to_model(installation, GetGithubInstallationOutput) for installation in installation_list]
    output_list = GetGithubInstallationOutputArray(results=mapped_list)
    return output_list


def get_organization_by_user(user_info: User):
    installation_list = get_github_installations_by_user(user_info.id)
    org_list = get_orgs_by_installations(installation_list)
    return org_list


def map_org_list_to_pydantic(org_list: List[Dict[str, Any]]):
    mapped_list = []
    for org in org_list:
        info = next(iter(org.values()))
        mapped_list.append(GithubOrgBasic(**info))

    github_org_list = GithubOrgBasicList(results=mapped_list)
    return github_org_list


def map_repo_list_to_pydantic(repo_list: List[Dict[str, Any]]):
    mapped_list = []
    for repo in repo_list:
        if repo is None:
            continue  # Skip None values

        try:
            info = next(iter(repo.values()))
            for _, value in info.items():
                mapped_list.append(Repository(**value))
        except (StopIteration, AttributeError, TypeError) as e:
            logger.error(f"Error processing repo {repo}: {e}")
            continue  # Skip problematic repos

    github_repo_list = RepositoryList(results=mapped_list)
    return github_repo_list


def get_repos_by_user(user_info: User, org_name: str):
    installation = get_github_installation_by_user_and_target_name(user_info.id, org_name)
    repo_list = get_repos_by_installation(installation)
    return repo_list


def map_branches_to_pydantic(branches: Dict[str, Any]) -> BranchList:
    mapped_list = []
    for branch in branches["branches"]:
        branch = Branch(**branch)
        mapped_list.append(branch)

    branch_list = BranchList(results=mapped_list)
    return branch_list


def map_azure_org_list_to_pydantic(org_list: List[Dict[str, Any]]):
    """Map Azure organization list to Pydantic models."""
    mapped_list = []
    for org in org_list:
        mapped_list.append(AzureOrgBasic(**org))

    azure_org_list = AzureOrgBasicList(results=mapped_list)
    return azure_org_list


def map_azure_repo_list_to_pydantic(repo_list: List[Dict[str, Any]]):
    """Map Azure repository list to Pydantic models."""
    mapped_list = []
    for repo in repo_list:
        if repo is None:
            continue  # Skip None values

        try:
            mapped_list.append(Repository(**repo))
        except (AttributeError, TypeError) as e:
            logger.error(f"Error processing Azure repo {repo}: {e}")
            continue  # Skip problematic repos

    azure_repo_list = RepositoryList(results=mapped_list)
    return azure_repo_list


def map_azure_branches_to_pydantic(branches: List[Dict[str, Any]]) -> BranchList:
    """Map Azure branches list to Pydantic models."""
    mapped_list = []
    for branch in branches:
        try:
            mapped_list.append(Branch(**branch))
        except (AttributeError, TypeError) as e:
            logger.error(f"Error processing Azure branch {branch}: {e}")
            continue  # Skip problematic branches

    branch_list = BranchList(results=mapped_list)
    return branch_list


def map_azure_project_list_to_pydantic(project_list: List[Dict[str, Any]]):
    """Map Azure project list to Pydantic models."""
    projects = []
    for project in project_list:
        projects.append(
            AzureProject(
                id=project.get("id", ""),
                name=project.get("name", ""),
                description=project.get("description", ""),
                url=project.get("url", ""),
            )
        )
    return AzureProjectList(results=projects)
