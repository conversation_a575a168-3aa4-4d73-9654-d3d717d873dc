from blitzy_utils.logger import logger
from blitzy_utils.service_client import ServiceClient
from common_models.models import VersionControlSystem
from flask import Blueprint
from flask_utils.decorators import flask_pydantic_response
from flask_utils.models_config.model_utils import map_to_model
from common_models.models import VersionControlSystem

from src.api.models import GetGithubInstallationOutput
from src.error.errors import (GitHubIntegrationNotFoundError,
                              check_service_error)
from src.middleware.decorators import get_user_info
from src.service.git_installation_service import \
    get_latest_git_integration_by_user_id_and_svc_type
from src.service.github_installation_service import \
    get_latest_github_integration_by_user_id
from src.service.github_installation_access_service import (
    get_active_github_installation_by_user_id,
)

# Note: get_github_project_repo_by_repo_id import removed since we can get branches
# directly from Azure DevOps repositories without requiring project name lookup

github_bp = Blueprint("github", __name__, url_prefix="/v1/github")


@github_bp.route("", methods=["GET"])
@get_user_info()
@flask_pydantic_response
def get_github_installation_info(user_info):
    user_id = user_info["id"]
    github_installation = get_latest_git_integration_by_user_id_and_svc_type(
        user_id,
        VersionControlSystem.GITHUB,
    )

    if not github_installation:
        logger.warning(f"Failed to find github integration info for user {user_id}")
        raise GitHubIntegrationNotFoundError("Failed to find github integration info.")

    gi_pydantic = map_to_model(github_installation, GetGithubInstallationOutput)
    return gi_pydantic, 200


@github_bp.route("/accounts", methods=["GET"])
@get_user_info()
@flask_pydantic_response
def get_github_accounts(user_info):
    """
    Get accounts/organizations for the authenticated user.

    This endpoint checks the user's installation type (GitHub or Azure DevOps)
    and routes to the appropriate handler.
    """
    user_id = user_info["id"]

    # Get the user's active installation to determine the service type
    installation = get_active_github_installation_by_user_id(user_id)
    if not installation:
        raise GitHubIntegrationNotFoundError("No active installation found for user")

    # Route based on service type
    if installation.svc_type == VersionControlSystem.AZURE_DEVOPS:
        response = get_organizations_from_azure_handler(user_id)
    else:  # Default to GitHub
        response = get_organizations_from_github_handler(user_id)

    return response, 200


@github_bp.route("/accounts/<account_name>/repositories", methods=["GET"])
@get_user_info()
@flask_pydantic_response
def get_repositories_by_account_name(user_info, account_name):
    """
    Get repositories from a specific account/organization.

    For Azure DevOps: This endpoint is deprecated in favor of project-based endpoints.
    For GitHub: Returns repositories directly from the organization.
    """
    user_id = user_info["id"]

    # Get the user's active installation to determine the service type
    installation = get_active_github_installation_by_user_id(user_id)
    if not installation:
        raise GitHubIntegrationNotFoundError("No active installation found for user")

    # Route based on service type
    if installation.svc_type == VersionControlSystem.AZURE_DEVOPS:
        # For Azure DevOps, get repositories from all projects in the organization
        response = get_all_repos_from_azure_org(user_id, account_name)
    else:  # GitHub
        response = get_repos_from_github_handler(user_id, account_name)

    return response, 200


@github_bp.route("/accounts/<account_name>/repositories/<repo_id>/branches", methods=["GET"])
@get_user_info()
@flask_pydantic_response
def get_branches_by_repo_name(user_info, account_name, repo_id):
    """
    Get branches from a specific repository.

    For Azure DevOps: Attempts to find the project from the database and route to Azure handler.
    For GitHub: Returns branches directly from the repository.
    """
    user_id = user_info["id"]

    # Get the user's active installation to determine the service type
    installation = get_active_github_installation_by_user_id(user_id)
    if not installation:
        raise GitHubIntegrationNotFoundError("No active installation found for user")

    # Route based on service type
    if installation.svc_type == VersionControlSystem.AZURE_DEVOPS:
        # For Azure DevOps, get branches directly using repository ID
        response = get_branches_from_azure_repo_handler(user_id, account_name, repo_id)
    else:  # GitHub
        response = get_branches_from_github_handler(user_id, account_name, repo_id)

    return response, 200


def get_organizations_from_github_handler(user_id: str):
    endpoint = f"/v1/github/users/{user_id}/organizations"
    with ServiceClient() as client:
        response = client.get("github", endpoint)
        check_service_error(response)
        return response.json()


def get_repos_from_github_handler(user_id: str, account_name: str):
    with ServiceClient() as client:
        response = client.get("github", f"/v1/github/users/{user_id}/organizations/{account_name}/repositories")
        check_service_error(response)
        return response.json()


def get_branches_from_github_handler(user_id: str, account_name: str, repo_id: str):
    with ServiceClient() as client:
        response = client.get(
            "github",
            f"/v1/github/users/{user_id}/organizations/{account_name}/repositories/{repo_id}/branches"
        )
        check_service_error(response)
        return response.json()


# Azure DevOps handler functions
def get_organizations_from_azure_handler(user_id: str):
    """
    Call archie-github-handler service to get Azure DevOps organizations for a user.
    """
    with ServiceClient() as client:
        response = client.get("github", f"/v1/github/users/{user_id}/organizations")
        check_service_error(response)
        return response.json()


def get_repos_from_azure_handler(user_id: str, account_name: str, project_id: str):
    """
    Call archie-github-handler service to get repositories from Azure DevOps project.
    """
    with ServiceClient() as client:
        response = client.get(
            "github",
            f"/v1/github/users/{user_id}/organizations/{account_name}/projects/{project_id}/repositories",
        )
        check_service_error(response)
        return response.json()


def get_branches_from_azure_handler(
    user_id: str, account_name: str, project_id: str, repo_id: str
):
    """
    Call archie-github-handler service to get branches from Azure DevOps repository.
    """
    with ServiceClient() as client:
        response = client.get(
            "github",
            f"/v1/github/users/{user_id}/organizations/{account_name}/projects/{project_id}/repositories/{repo_id}/branches",
        )
        check_service_error(response)
        return response.json()


def get_branches_from_azure_repo_handler(user_id: str, account_name: str, repo_id: str):
    """
    Call archie-github-handler service to get branches from Azure DevOps repository
    directly using repository ID without requiring project name.

    This uses the Azure endpoint that can get branches directly from a repository
    without needing to specify the project name.
    """
    with ServiceClient() as client:
        response = client.get(
            "github",
            f"/v1/github/users/{user_id}/organizations/{account_name}/repositories/{repo_id}/branches",
        )
        check_service_error(response)
        return response.json()


def get_all_repos_from_azure_org(user_id: str, account_name: str):
    """
    Get all repositories from an Azure DevOps organization by aggregating
    repositories from all projects within the organization.

    This function:
    1. Gets all projects in the organization
    2. For each project, gets all repositories
    3. Aggregates and returns all repositories

    Args:
        user_id: User ID
        account_name: Azure DevOps organization name

    Returns:
        Aggregated list of repositories from all projects
    """
    try:
        # First, get all projects in the organization
        with ServiceClient() as client:
            projects_response = client.get(
                "github",
                f"/v1/github/users/{user_id}/organizations/{account_name}/projects",
            )
            check_service_error(projects_response)
            projects_data = projects_response.json()

        all_repositories = []

        # For each project, get its repositories
        for project in projects_data.get("results", []):
            project_id = project.get("id")
            project_name = project.get("name")
            if project_id:
                try:
                    with ServiceClient() as client:
                        repos_response = client.get(
                            "github",
                            f"/v1/github/users/{user_id}/organizations/{account_name}/projects/{project_id}/repositories",
                        )
                        check_service_error(repos_response)
                        repos_data = repos_response.json()

                        # Add project context to each repository
                        for repo in repos_data.get("results", []):
                            repo["project_id"] = project_id
                            repo["project_name"] = (
                                project_name  # Keep for backward compatibility
                            )
                            all_repositories.append(repo)

                except Exception as e:
                    logger.warning(
                        f"Failed to get repositories for project {project_name} (ID: {project_id}): {str(e)}"
                    )
                    continue

        return {"results": all_repositories}

    except Exception as e:
        logger.error(
            f"Error getting repositories from Azure organization {account_name}: {str(e)}"
        )
        raise GitHubIntegrationNotFoundError(
            f"Failed to get repositories from Azure organization: {str(e)}"
        )


def get_azure_project_name_from_repo(repo_id: str) -> str:
    """
    Get Azure project name from repository ID by querying the GitHubProjectRepo table.

    This function looks up the azure_project_id from the database and extracts
    the project name from the stored Azure metadata.

    Args:
        repo_id: Azure DevOps repository ID

    Returns:
        Project name if found, None otherwise
    """
    try:
        # Get the project repo record from the database
        project_repo = get_github_project_repo_by_repo_id(repo_id)

        if (
            project_repo
            and hasattr(project_repo, "azure_project_id")
            and project_repo.azure_project_id
        ):
            # For now, we'll need to extract project name from the metadata or use a different approach
            # Since we don't have the project name directly stored, we'll return None
            # This will trigger the deprecation message
            logger.warning(
                f"Azure project lookup not fully implemented for repo {repo_id}"
            )
            return None

        return None
    except Exception as e:
        logger.error(f"Error looking up Azure project for repo {repo_id}: {str(e)}")
        return None
